# 架构变更说明

## 变更概述

将 OAuth2 认证逻辑从前端完全移至后端处理，前端只负责令牌管理和 API 调用。

## 主要变更

### 1. 删除的文件
- `src/config/oauth.js` - OAuth 配置文件
- `src/views/OAuth.vue` - OAuth 页面组件

### 2. 修改的文件

#### `src/services/authService.js`
- 移除 OAuth 授权码相关逻辑
- 改为基于后端令牌的认证管理
- 添加 `getAuthHeader()` 方法用于 API 请求

#### `src/stores/auth.js`
- 使用 `getApiUrl()` 构建认证 URL，遵循全局 base URL 配置
- 改为处理后端返回的令牌和用户信息
- 添加认证回调结果处理
- 增加开发环境调试日志

#### `src/router/index.js`
- 移除 `/oauth` 路由
- 路由守卫直接调用 `startAuth()` 跳转到后端认证

#### `src/config/http.js`
- 请求拦截器改为添加 `Authorization: Bearer {token}` 头部
- 401 错误处理改为跳转到后端认证接口

#### `src/config/api.js`
- 添加 OAuth 相关端点配置

#### `src/main.js`
- 添加认证回调消息提示

#### 环境变量文件
- 移除前端 OAuth 配置参数
- 添加说明注释

## 新的认证流程

### 1. 用户访问需要认证的页面
```
用户访问 /user/123 → 路由守卫检查认证状态 → 未认证则跳转到后端认证接口
```

### 2. 后端处理 OAuth 流程
```
GET /api/oauth/start?backto={前端URL}
↓
后端生成 OAuth URL 并重定向到微信
↓
微信回调到后端 /api/oauth/callback
↓
后端处理授权码，获取用户信息，生成前端令牌
↓
重定向回前端页面，携带 token 和 user 参数
```

### 3. 前端处理认证回调
```
前端接收 URL 参数：?token=xxx&user=xxx&auth_success=true
↓
解析参数，保存到 localStorage
↓
清理 URL 参数，显示成功消息
↓
用户可以正常访问需要认证的功能
```

## API 调用流程

### 1. 自动添加认证头部
```javascript
// HTTP 拦截器自动添加
headers: {
  'Authorization': 'Bearer {token}'
}
```

### 2. 401 错误自动处理
```javascript
// 自动清理本地认证信息并重新认证
if (error.response?.status === 401) {
  localStorage.removeItem('auth_token')
  localStorage.removeItem('user_info')
  window.location.href = `${API_BASE_URL}/api/oauth/start?backto=${currentUrl}`
}
```

## 优势

1. **安全性提升**：敏感的 OAuth 配置和处理逻辑在后端
2. **架构清晰**：前后端职责分离明确
3. **配置统一**：所有 API 调用都遵循全局 base URL 配置
4. **错误处理**：统一的认证错误处理机制
5. **用户体验**：认证状态持久化，页面刷新不丢失登录状态

## 后端需要实现的接口

1. `GET /api/oauth/start?backto={url}` - 启动 OAuth 流程
2. `GET /api/oauth/callback?code={code}&state={state}` - 处理 OAuth 回调
3. `GET /api/tweets` - 微博数据接口（需要 Bearer token 认证）

## 部署注意事项

1. 确保后端正确配置 OAuth 参数
2. 前端的 `VITE_API_BASE_URL` 需要指向后端服务
3. 后端需要处理 CORS（如果前后端不同域）
4. 生产环境需要 HTTPS