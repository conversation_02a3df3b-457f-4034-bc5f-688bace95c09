// 工具类样式 - 原子化样式类

@use './variables.scss' as *;
@use './mixins.scss' as *;

// 间距工具类
.m-0 { margin: 0; }
.m-xs { margin: $space-xs; }
.m-sm { margin: $space-sm; }
.m-md { margin: $space-md; }
.m-lg { margin: $space-lg; }
.m-xl { margin: $space-xl; }
.m-2xl { margin: $space-2xl; }

.mt-0 { margin-top: 0; }
.mt-xs { margin-top: $space-xs; }
.mt-sm { margin-top: $space-sm; }
.mt-md { margin-top: $space-md; }
.mt-lg { margin-top: $space-lg; }
.mt-xl { margin-top: $space-xl; }
.mt-2xl { margin-top: $space-2xl; }

.mr-0 { margin-right: 0; }
.mr-xs { margin-right: $space-xs; }
.mr-sm { margin-right: $space-sm; }
.mr-md { margin-right: $space-md; }
.mr-lg { margin-right: $space-lg; }
.mr-xl { margin-right: $space-xl; }
.mr-2xl { margin-right: $space-2xl; }

.mb-0 { margin-bottom: 0; }
.mb-xs { margin-bottom: $space-xs; }
.mb-sm { margin-bottom: $space-sm; }
.mb-md { margin-bottom: $space-md; }
.mb-lg { margin-bottom: $space-lg; }
.mb-xl { margin-bottom: $space-xl; }
.mb-2xl { margin-bottom: $space-2xl; }

.ml-0 { margin-left: 0; }
.ml-xs { margin-left: $space-xs; }
.ml-sm { margin-left: $space-sm; }
.ml-md { margin-left: $space-md; }
.ml-lg { margin-left: $space-lg; }
.ml-xl { margin-left: $space-xl; }
.ml-2xl { margin-left: $space-2xl; }

.mx-0 { margin-left: 0; margin-right: 0; }
.mx-xs { margin-left: $space-xs; margin-right: $space-xs; }
.mx-sm { margin-left: $space-sm; margin-right: $space-sm; }
.mx-md { margin-left: $space-md; margin-right: $space-md; }
.mx-lg { margin-left: $space-lg; margin-right: $space-lg; }
.mx-xl { margin-left: $space-xl; margin-right: $space-xl; }
.mx-2xl { margin-left: $space-2xl; margin-right: $space-2xl; }
.mx-auto { margin-left: auto; margin-right: auto; }

.my-0 { margin-top: 0; margin-bottom: 0; }
.my-xs { margin-top: $space-xs; margin-bottom: $space-xs; }
.my-sm { margin-top: $space-sm; margin-bottom: $space-sm; }
.my-md { margin-top: $space-md; margin-bottom: $space-md; }
.my-lg { margin-top: $space-lg; margin-bottom: $space-lg; }
.my-xl { margin-top: $space-xl; margin-bottom: $space-xl; }
.my-2xl { margin-top: $space-2xl; margin-bottom: $space-2xl; }

.p-0 { padding: 0; }
.p-xs { padding: $space-xs; }
.p-sm { padding: $space-sm; }
.p-md { padding: $space-md; }
.p-lg { padding: $space-lg; }
.p-xl { padding: $space-xl; }
.p-2xl { padding: $space-2xl; }

.pt-0 { padding-top: 0; }
.pt-xs { padding-top: $space-xs; }
.pt-sm { padding-top: $space-sm; }
.pt-md { padding-top: $space-md; }
.pt-lg { padding-top: $space-lg; }
.pt-xl { padding-top: $space-xl; }
.pt-2xl { padding-top: $space-2xl; }

.pr-0 { padding-right: 0; }
.pr-xs { padding-right: $space-xs; }
.pr-sm { padding-right: $space-sm; }
.pr-md { padding-right: $space-md; }
.pr-lg { padding-right: $space-lg; }
.pr-xl { padding-right: $space-xl; }
.pr-2xl { padding-right: $space-2xl; }

.pb-0 { padding-bottom: 0; }
.pb-xs { padding-bottom: $space-xs; }
.pb-sm { padding-bottom: $space-sm; }
.pb-md { padding-bottom: $space-md; }
.pb-lg { padding-bottom: $space-lg; }
.pb-xl { padding-bottom: $space-xl; }
.pb-2xl { padding-bottom: $space-2xl; }

.pl-0 { padding-left: 0; }
.pl-xs { padding-left: $space-xs; }
.pl-sm { padding-left: $space-sm; }
.pl-md { padding-left: $space-md; }
.pl-lg { padding-left: $space-lg; }
.pl-xl { padding-left: $space-xl; }
.pl-2xl { padding-left: $space-2xl; }

.px-0 { padding-left: 0; padding-right: 0; }
.px-xs { padding-left: $space-xs; padding-right: $space-xs; }
.px-sm { padding-left: $space-sm; padding-right: $space-sm; }
.px-md { padding-left: $space-md; padding-right: $space-md; }
.px-lg { padding-left: $space-lg; padding-right: $space-lg; }
.px-xl { padding-left: $space-xl; padding-right: $space-xl; }
.px-2xl { padding-left: $space-2xl; padding-right: $space-2xl; }

.py-0 { padding-top: 0; padding-bottom: 0; }
.py-xs { padding-top: $space-xs; padding-bottom: $space-xs; }
.py-sm { padding-top: $space-sm; padding-bottom: $space-sm; }
.py-md { padding-top: $space-md; padding-bottom: $space-md; }
.py-lg { padding-top: $space-lg; padding-bottom: $space-lg; }
.py-xl { padding-top: $space-xl; padding-bottom: $space-xl; }
.py-2xl { padding-top: $space-2xl; padding-bottom: $space-2xl; }

// 文字工具类
.text-xs { font-size: $font-size-xs; }
.text-sm { font-size: $font-size-sm; }
.text-base { font-size: $font-size-base; }
.text-md { font-size: $font-size-md; }
.text-lg { font-size: $font-size-lg; }
.text-xl { font-size: $font-size-xl; }
.text-2xl { font-size: $font-size-2xl; }

.text-primary { color: $text-primary; }
.text-secondary { color: $text-secondary; }
.text-tertiary { color: $text-tertiary; }
.text-disabled { color: $text-disabled; }
.text-brand { color: $primary-color; }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.leading-tight { line-height: $line-height-tight; }
.leading-normal { line-height: $line-height-base; }
.leading-relaxed { line-height: $line-height-relaxed; }

.text-ellipsis { @include text-ellipsis; }
.text-clamp-1 { @include text-clamp(1); }
.text-clamp-2 { @include text-clamp(2); }
.text-clamp-3 { @include text-clamp(3); }

// 布局工具类
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.hidden { display: none; }

.flex-row { flex-direction: row; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }
.flex-shrink-0 { flex-shrink: 0; }
.flex-grow { flex-grow: 1; }

.gap-0 { gap: 0; }
.gap-xs { gap: $space-xs; }
.gap-sm { gap: $space-sm; }
.gap-md { gap: $space-md; }
.gap-lg { gap: $space-lg; }
.gap-xl { gap: $space-xl; }
.gap-2xl { gap: $space-2xl; }

// 定位工具类
.static { position: static; }
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }

.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

// 尺寸工具类
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-fit { width: fit-content; }
.w-screen { width: 100vw; }

.h-full { height: 100%; }
.h-auto { height: auto; }
.h-fit { height: fit-content; }
.h-screen { height: 100vh; }

.min-w-0 { min-width: 0; }
.min-w-full { min-width: 100%; }
.min-h-0 { min-height: 0; }
.min-h-full { min-height: 100%; }
.min-h-screen { min-height: 100vh; }

.max-w-none { max-width: none; }
.max-w-xs { max-width: 320px; }
.max-w-sm { max-width: 384px; }
.max-w-md { max-width: 448px; }
.max-w-lg { max-width: 512px; }
.max-w-xl { max-width: 576px; }
.max-w-2xl { max-width: 672px; }
.max-w-3xl { max-width: 768px; }
.max-w-4xl { max-width: 896px; }
.max-w-5xl { max-width: 1024px; }
.max-w-6xl { max-width: 1152px; }
.max-w-7xl { max-width: 1280px; }
.max-w-full { max-width: 100%; }

// 背景工具类
.bg-primary { background-color: $bg-primary; }
.bg-secondary { background-color: $bg-secondary; }
.bg-tertiary { background-color: $bg-tertiary; }
.bg-brand { background-color: $primary-color; }
.bg-transparent { background-color: transparent; }

// 边框工具类
.border { border: 1px solid $border-default; }
.border-0 { border: 0; }
.border-t { border-top: 1px solid $border-default; }
.border-r { border-right: 1px solid $border-default; }
.border-b { border-bottom: 1px solid $border-default; }
.border-l { border-left: 1px solid $border-default; }

.border-light { border-color: $border-light; }
.border-default { border-color: $border-default; }
.border-dark { border-color: $border-dark; }
.border-brand { border-color: $primary-color; }

.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: $radius-sm; }
.rounded { border-radius: $radius-md; }
.rounded-lg { border-radius: $radius-lg; }
.rounded-xl { border-radius: $radius-xl; }
.rounded-full { border-radius: 50%; }

// 阴影工具类
.shadow-none { box-shadow: none; }
.shadow-1 { box-shadow: $shadow-1; }
.shadow-2 { box-shadow: $shadow-2; }
.shadow-3 { box-shadow: $shadow-3; }
.shadow-hover { box-shadow: $shadow-hover; }

// 透明度工具类
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

// 变换工具类
.transform { transform: translateZ(0); }
.scale-95 { transform: scale(0.95); }
.scale-100 { transform: scale(1); }
.scale-105 { transform: scale(1.05); }
.scale-110 { transform: scale(1.1); }

.rotate-0 { transform: rotate(0deg); }
.rotate-90 { transform: rotate(90deg); }
.rotate-180 { transform: rotate(180deg); }
.rotate-270 { transform: rotate(270deg); }

// 过渡工具类
.transition-none { transition: none; }
.transition-all { transition: all $transition-base; }
.transition-colors { transition: color $transition-base, background-color $transition-base, border-color $transition-base; }
.transition-opacity { transition: opacity $transition-base; }
.transition-shadow { transition: box-shadow $transition-base; }
.transition-transform { transition: transform $transition-base; }

.duration-fast { transition-duration: $transition-fast; }
.duration-base { transition-duration: $transition-base; }
.duration-slow { transition-duration: $transition-slow; }

// 光标工具类
.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-not-allowed { cursor: not-allowed; }

// 用户选择工具类
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }
.select-auto { user-select: auto; }

// 溢出工具类
.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }

.overflow-x-auto { overflow-x: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-x-visible { overflow-x: visible; }
.overflow-x-scroll { overflow-x: scroll; }

.overflow-y-auto { overflow-y: auto; }
.overflow-y-hidden { overflow-y: hidden; }
.overflow-y-visible { overflow-y: visible; }
.overflow-y-scroll { overflow-y: scroll; }

// 可访问性工具类
.sr-only {
  @include sr-only;
}

.not-sr-only {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

// 响应式工具类
@include mobile {
  .mobile\:hidden { display: none; }
  .mobile\:block { display: block; }
  .mobile\:flex { display: flex; }
  .mobile\:grid { display: grid; }
  
  .mobile\:text-xs { font-size: $font-size-xs; }
  .mobile\:text-sm { font-size: $font-size-sm; }
  .mobile\:text-base { font-size: $font-size-base; }
  
  .mobile\:p-xs { padding: $space-xs; }
  .mobile\:p-sm { padding: $space-sm; }
  .mobile\:p-md { padding: $space-md; }
  
  .mobile\:m-xs { margin: $space-xs; }
  .mobile\:m-sm { margin: $space-sm; }
  .mobile\:m-md { margin: $space-md; }
}

@include tablet {
  .tablet\:hidden { display: none; }
  .tablet\:block { display: block; }
  .tablet\:flex { display: flex; }
  .tablet\:grid { display: grid; }
  
  .tablet\:text-sm { font-size: $font-size-sm; }
  .tablet\:text-base { font-size: $font-size-base; }
  .tablet\:text-lg { font-size: $font-size-lg; }
  
  .tablet\:p-md { padding: $space-md; }
  .tablet\:p-lg { padding: $space-lg; }
  .tablet\:p-xl { padding: $space-xl; }
}

@include desktop {
  .desktop\:hidden { display: none; }
  .desktop\:block { display: block; }
  .desktop\:flex { display: flex; }
  .desktop\:grid { display: grid; }
}

// 打印工具类
@media print {
  .print\:hidden { display: none !important; }
  .print\:block { display: block !important; }
}