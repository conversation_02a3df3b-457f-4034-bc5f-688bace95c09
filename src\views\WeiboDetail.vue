<template>
  <div class="weibo-detail">
    <!-- 内容区域 -->
    <div class="container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <!-- 显示骨架屏作为背景 -->
        <div class="error-skeleton">
          <el-skeleton :rows="5" animated />
        </div>
        
        <!-- 错误信息覆盖层 -->
        <div class="error-overlay">
          <el-alert
            title="加载失败"
            :description="error"
            type="error"
            show-icon
            :closable="false"
          />
          <div class="error-actions">
            <el-button @click="loadData" type="primary">重试</el-button>
          </div>
        </div>
      </div>

      <!-- 成功状态 - 微博卡片 -->
      <div v-else-if="card" class="detail-content">
        <weibo-card 
          :card="card" 
          :is-visible="true" 
          :show-actions="true"
          :show-user-profile="true"
        ></weibo-card>

        <!-- 警告信息 -->
        <div class="warning-info">
          <el-alert
            title="隐私提醒"
            description="如果你的浏览器登录了微博，可能会在对方主页留下访客记录！"
            type="warning"
            show-icon
            :closable="false"
          />
        </div>
      </div>

      <!-- 无数据状态 -->
      <div v-else class="empty-container">
        <el-empty description="未找到该微博" />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import WeiboCard from '../components/WeiboCard.vue'
import httpClient from '../config/http.js'
import { API_CONFIG, isDevelopment } from '../config/api.js'

export default {
  name: 'WeiboDetail',
  components: {
    WeiboCard
  },
  props: {
    weiboId: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const router = useRouter()
    const route = useRoute()
    
    const card = ref(null)
    const loading = ref(true)
    const error = ref('')

    const loadData = async () => {
      try {
        loading.value = true
        error.value = ''
        card.value = null
        
        // 获取微博ID，优先使用props，其次使用route.params
        const weiboId = props.weiboId || route.params.weiboId
        
        console.log('加载微博详情，ID:', weiboId)
        console.log('Props weiboId:', props.weiboId)
        console.log('Route params:', route.params)
        
        if (!weiboId) {
          throw new Error('微博ID参数缺失')
        }
        
        // 仿照 single_card.html 的请求逻辑，使用配置的 httpClient
        const response = await httpClient.get(`/data/weibo/ajax/${weiboId}.json`)
        
        console.log('API响应:', response)
        
        if (response.data) {
          card.value = response.data
          console.log('微博详情加载成功:', response.data)
        } else {
          throw new Error('微博数据为空')
        }
      } catch (err) {
        console.error('加载微博详情失败:', err)
        
        // 设置错误信息，但保持 loading 为 false 以显示错误状态
        const errorMessage = err.response?.status === 404 
          ? '微博不存在或已被删除' 
          : err.response?.data?.message || err.message || '加载失败，请重试'
        
        error.value = errorMessage
        
        // 显示错误消息
        ElMessage.error('加载微博详情失败')
      } finally {
        loading.value = false
      }
    }





    // 监听路由参数变化
    watch(() => route.params.weiboId, (newWeiboId) => {
      console.log('路由参数变化:', newWeiboId)
      if (newWeiboId) {
        loadData()
      }
    }, { immediate: true })

    // 监听props变化
    watch(() => props.weiboId, (newWeiboId) => {
      console.log('Props参数变化:', newWeiboId)
      if (newWeiboId) {
        loadData()
      }
    }, { immediate: true })

    onMounted(() => {
      console.log('WeiboDetail组件已挂载')
      console.log('当前props:', props)
      console.log('当前route.params:', route.params)
      
      // 确保在组件挂载时加载数据
      const weiboId = props.weiboId || route.params.weiboId
      if (weiboId) {
        loadData()
      } else {
        error.value = '微博ID参数错误'
        loading.value = false
      }
    })

    return {
      card,
      loading,
      error,
      loadData
    }
  }
}
</script>

<style lang="scss" scoped>
.weibo-detail {
  min-height: 100vh;
  padding-top: $space-xl;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 $space-lg;
  
  @media (max-width: #{$breakpoint-mobile - 1px}) {
    padding: 0 $space-md;
  }
}

.loading-container {
  padding: $space-2xl 0;

  .el-skeleton {
    @include card-base;
    padding: $space-xl;
    margin-bottom: $space-lg;
    border-radius: $radius-xl;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: $shadow-1;
  }
}

.error-container {
  position: relative;
  padding: $space-2xl 0;
  min-height: 400px;
  
  .error-skeleton {
    opacity: 0.2;
    pointer-events: none;
    
    .el-skeleton {
      @include card-base;
      padding: $space-xl;
      margin-bottom: $space-lg;
    }
  }
  
  .error-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 500px;
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    padding: $space-2xl;
    border-radius: $radius-xl;
    box-shadow: $shadow-hover;
    backdrop-filter: blur(10px);
    z-index: 10;
    
    @include mobile {
      width: 95%;
      padding: $space-xl;
    }
  }
  
  .error-actions {
    margin-top: $space-xl;
    display: flex;
    justify-content: center;
  }
}

.detail-content {
  animation: fadeInUp 0.6s ease-out;
  margin-bottom: $space-xl;

  // 添加微妙的阴影效果
  .weibo-card {
    box-shadow: $shadow-2;
    border-radius: $radius-xl;
    overflow: hidden;
    transition: all $transition-base;

    &:hover {
      transform: translateY(-2px);
      box-shadow: $shadow-hover;
    }
  }
}

.warning-info {
  margin-top: $space-xl;

  .el-alert {
    border-radius: $radius-lg;
    border: none;
    background: linear-gradient(135deg,
      rgba(245, 166, 35, 0.1) 0%,
      rgba(245, 166, 35, 0.05) 100%);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    box-shadow: $shadow-1;
  }
}

.empty-container {
  padding: $space-2xl 0;
  text-align: center;

  .el-empty {
    padding: $space-2xl;
    background: rgba(255, 255, 255, 0.8);
    border-radius: $radius-xl;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: $shadow-1;
    margin: 0 auto;
    max-width: 400px;
  }
}

// 响应式适配
@include mobile {
  .weibo-detail {
    padding-top: $space-md;
  }

  .container {
    padding: 0 $space-sm;
  }

  .detail-content .weibo-card {
    border-radius: $radius-lg;

    &:hover {
      transform: none; // 移动端禁用悬停效果
    }
  }

  .warning-info .el-alert {
    border-radius: $radius-md;
  }

  .loading-container .el-skeleton,
  .empty-container .el-empty {
    border-radius: $radius-lg;
  }
}

// 暗色模式适配
@include dark-mode {
  .weibo-detail {
    background: linear-gradient(135deg,
      rgba(30, 41, 59, 0.8) 0%,
      rgba(15, 23, 42, 0.9) 100%);
  }

  .loading-container .el-skeleton,
  .empty-container .el-empty {
    background: rgba(30, 41, 59, 0.8);
  }

  .warning-info .el-alert {
    background: linear-gradient(135deg,
      rgba(245, 166, 35, 0.15) 0%,
      rgba(245, 166, 35, 0.08) 100%);
  }
}

// 页面进入动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>