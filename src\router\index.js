import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import UserProfile from '../views/UserProfile.vue'
import WeiboDetail from '../views/WeiboDetail.vue'
import NotFound from '../views/NotFound.vue'
import AuthCallback from '../components/AuthCallback.vue'
import { useAuth } from '../stores/auth.js'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/auth/callback',
    name: 'AuthCallback',
    component: AuthCallback
  },
  {
    path: '/user/:userId',
    name: 'UserProfile',
    component: UserProfile,
    props: true,
    meta: { requiresAuth: true },
    beforeEnter: (to, from, next) => {
      // 验证用户ID是否为数字
      const userId = to.params.userId
      if (!/^\d+$/.test(userId)) {
        next('/404')
      } else {
        next()
      }
    }
  },
  {
    path: '/detail/:weiboId',
    name: 'WeiboDetail',
    component: WeiboDetail,
    props: true,
    meta: { requiresAuth: true },
    beforeEnter: (to, from, next) => {
      // 验证微博ID是否存在
      const weiboId = to.params.weiboId
      if (!weiboId || weiboId.trim() === '') {
        next('/404')
      } else {
        next()
      }
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: NotFound
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局路由守卫
router.beforeEach((to, from, next) => {
  const { isAuthenticated, startAuth } = useAuth()
  
  // 检查路由是否需要认证
  if (to.meta.requiresAuth && !isAuthenticated.value) {
    // 需要认证但未认证，跳转到后端认证接口
    startAuth()
    return
  }
  
  next()
})

export default router