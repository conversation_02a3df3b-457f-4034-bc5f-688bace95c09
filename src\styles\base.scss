// 基础样式重置和全局样式

@use './variables.scss' as *;
@use './mixins.scss' as *;

// CSS Reset 和基础样式
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  line-height: $line-height-base;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  padding: 0;
  font-family: $font-family-base;
  font-size: $font-size-base;
  line-height: $line-height-base;
  color: $text-primary;
  background: linear-gradient(135deg, $bg-secondary 0%, $bg-tertiary 100%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
}

// 应用根元素
#app {
  min-height: 100vh;
  animation: pageEnter 0.8s ease-out;
}

// 标题重置
h1, h2, h3, h4, h5, h6 {
  @include heading-base;
}

h1 { font-size: $font-size-2xl; }
h2 { font-size: $font-size-xl; }
h3 { font-size: $font-size-lg; }
h4 { font-size: $font-size-md; }
h5 { font-size: $font-size-base; }
h6 { font-size: $font-size-sm; }

// 段落和文本
p {
  margin: 0 0 $space-md 0;
  @include body-text;
}

// 链接
a {
  color: $primary-color;
  text-decoration: none;
  transition: color $transition-fast;
  
  &:hover {
    color: $primary-hover;
  }
  
  &:focus-visible {
    @include focus-visible;
  }
}

// 按钮重置
button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  margin: 0;
  overflow: visible;
  text-transform: none;
  -webkit-appearance: button;
  
  &::-moz-focus-inner {
    border-style: none;
    padding: 0;
  }
  
  &:-moz-focusring {
    outline: 1px dotted ButtonText;
  }
}

// 输入框重置
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  margin: 0;
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

// 图片
img {
  max-width: 100%;
  height: auto;
  border-style: none;
}

// 列表
ul, ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

// 表格
table {
  border-collapse: collapse;
  border-spacing: 0;
}

// 选中文本样式
::selection {
  background: $primary-light;
  color: $primary-color;
}

::-moz-selection {
  background: $primary-light;
  color: $primary-color;
}

// 滚动条美化
html {
  @include custom-scrollbar;
}

// 焦点样式优化
*:focus {
  outline: 2px solid $primary-color;
  outline-offset: 2px;
  border-radius: $radius-sm;
}

*:focus:not(:focus-visible) {
  outline: none;
}

// 桌面端优化
@include desktop {
  // 鼠标悬停优化
  .card:hover {
    transform: translateY(-4px);
    box-shadow: $shadow-hover;
  }
  
  .el-button:hover {
    transform: translateY(-2px);
  }
  
  .el-input:hover {
    .el-input__wrapper {
      box-shadow: $shadow-2;
    }
  }
  
  // 键盘导航优化
  .el-button:focus-visible {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
    z-index: 1;
  }
  
  .el-input:focus-within {
    .el-input__wrapper {
      outline: 2px solid $primary-color;
      outline-offset: 2px;
    }
  }
  
  // 大屏幕布局优化
  .container {
    max-width: 900px;
    padding: 0 $space-2xl;
  }
  
  .user-cards {
    grid-template-columns: repeat(3, 1fr);
  }
}

// 动画定义
@keyframes pageEnter {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes loading-dots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes loading-pulse {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

// 暗色模式支持
@include dark-mode {
  body {
    color: $dark-text-primary;
    background: linear-gradient(135deg, $dark-bg-secondary 0%, $dark-bg-tertiary 100%);
  }
  
  h1, h2, h3, h4, h5, h6 {
    color: $dark-text-primary;
  }
  
  p {
    color: $dark-text-primary;
  }
}

// 高对比度模式支持
@include high-contrast {
  * {
    border-color: currentColor !important;
  }
  
  a {
    text-decoration: underline;
  }
}

// 减少动画模式支持
@include reduced-motion {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  #app {
    animation: none;
  }
}

// 打印样式优化
@media print {
  body {
    background: white !important;
    color: black !important;
  }
  
  * {
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a {
    text-decoration: underline;
  }
}