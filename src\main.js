import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'
import router from './router'
import { useAuth } from './stores/auth.js'
import './styles/index.scss'

// 开发环境调试工具
if (import.meta.env.DEV) {
  import('./utils/apiTest.js')
  import('./utils/authDebug.js')
  import('./utils/scrollDebug.js')
}

const app = createApp(App)

// Element Plus 全局配置
app.use(ElementPlus, {
  // 确保图片预览组件正确初始化
  zIndex: 3000
})

// 使用路由
app.use(router)

// 初始化认证状态
const { initAuth } = useAuth()
initAuth()

app.mount('#app')