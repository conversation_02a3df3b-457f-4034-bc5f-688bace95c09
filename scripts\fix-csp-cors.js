/**
 * CSP 和 CORS 问题修复脚本
 */

console.log('🔧 CSP/CORS 问题诊断和修复工具')

// 检查当前环境
const isDev = import.meta.env.DEV
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL

console.log('📊 环境信息:', {
  isDevelopment: isDev,
  apiBaseUrl,
  currentUrl: window.location.href
})

// 检查 CSP 设置
const checkCSP = () => {
  const metaTags = document.querySelectorAll('meta[http-equiv="Content-Security-Policy"]')
  console.log('🛡️ CSP 配置:', metaTags.length > 0 ? metaTags[0].content : '未设置')
}

// 测试 API 连接
const testConnection = async () => {
  try {
    const testUrl = isDev ? '/api/get_subscribe' : `${apiBaseUrl}/api/get_subscribe`
    console.log('🌐 测试连接:', testUrl)
    
    const response = await fetch(testUrl, {
      method: 'HEAD',
      mode: 'cors'
    })
    
    console.log('✅ 连接测试成功:', response.status)
    return true
  } catch (error) {
    console.error('❌ 连接测试失败:', error.message)
    return false
  }
}

// 提供修复建议
const provideFixes = () => {
  console.log('💡 修复建议:')
  
  if (isDev) {
    console.log('开发环境:')
    console.log('- 确保 vite.config.js 中配置了正确的代理')
    console.log('- 检查 VITE_API_BASE_URL 环境变量')
    console.log('- 重启开发服务器')
  } else {
    console.log('生产环境:')
    console.log('- 检查 vercel.json 中的 CSP 配置')
    console.log('- 确保 API 域名在 connect-src 中')
    console.log('- 验证环境变量配置')
  }
}

// 运行诊断
const runDiagnostics = async () => {
  checkCSP()
  const connectionOk = await testConnection()
  
  if (!connectionOk) {
    provideFixes()
  }
}

// 导出工具函数
export { checkCSP, testConnection, provideFixes, runDiagnostics }

// 在控制台中提供全局访问
if (typeof window !== 'undefined') {
  window.cspCorsFix = {
    checkCSP,
    testConnection,
    provideFixes,
    runDiagnostics
  }
  
  console.log('🎯 在控制台中运行 window.cspCorsFix.runDiagnostics() 进行完整诊断')
}