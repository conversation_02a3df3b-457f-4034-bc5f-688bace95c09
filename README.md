# Weibo Vue App

一个现代化的微博内容展示应用，基于 Vue 3 + Element Plus + Vite 构建。

## 🚀 特性

- **现代化架构**: Vue 3 Composition API + Vite 构建系统
- **组件化设计**: 模块化的组件结构，易于维护和扩展
- **响应式设计**: 适配桌面端和移动端
- **无限滚动**: 自动加载更多内容
- **搜索功能**: 支持关键词搜索微博内容
- **图片预览**: 支持图片放大预览
- **转发展示**: 完整的转发内容显示
- **性能优化**: 图片懒加载、虚拟滚动等优化

## 📦 技术栈

- **前端框架**: Vue 3
- **UI 组件库**: Element Plus
- **构建工具**: Vite
- **HTTP 客户端**: Axios
- **文本处理**: DOMPurify
- **时间处理**: Luxon

## 🛠️ 开发

### 环境要求

- Node.js >= 16
- npm >= 8

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 📁 项目结构

```
├── src/
│   ├── components/          # Vue 组件
│   │   └── WeiboCard.vue   # 微博卡片组件
│   ├── services/           # API 服务层
│   │   └── weiboService.js # 微博 API 服务
│   ├── utils/              # 工具函数
│   │   ├── imageProxy.js   # 图片代理工具
│   │   └── textFormatter.js # 文本格式化工具
│   ├── styles/             # 样式文件
│   │   └── global.css      # 全局样式
│   ├── App.vue             # 根组件
│   └── main.js             # 应用入口
├── examples/               # 示例代码
│   ├── single_page.html    # 原始单页面版本
│   └── README.md           # 示例说明
├── index.html              # HTML 模板
├── vite.config.js          # Vite 配置
└── package.json            # 项目配置
```

## 🔧 配置

### API 配置

项目默认请求 `/api/tweets` 接口，你可以在 `src/services/weiboService.js` 中修改 API 地址。

### 图片代理

项目使用百度图片代理服务来解决跨域问题，你可以在 `src/utils/imageProxy.js` 中修改代理配置。

## 📖 使用说明

1. 访问应用后，系统会从 URL 中提取用户 ID
2. 在搜索框中输入关键词可以搜索相关微博
3. 向下滚动会自动加载更多内容
4. 点击图片可以预览大图
5. 点击"查看原文"可以跳转到微博原页面

## 🔄 项目演进

本项目从单页面 HTML 文件重构而来，演进过程：

1. **原始版本** (`examples/single_page.html`): Vue 2 + Element UI 单页面应用
2. **现代版本** (当前): Vue 3 + Element Plus + Vite 模块化应用

## 📄 许可证

MIT License