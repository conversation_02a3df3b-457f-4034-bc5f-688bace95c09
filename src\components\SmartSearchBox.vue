<template>
  <div
    class="smart-search-box"
    :class="{
      'is-hidden': shouldHide
    }"
  >
    <div class="search-container">
      <el-input 
        v-model="localKeyword" 
        placeholder="搜索微博内容..." 
        clearable 
        @keyup.enter="handleSearch"
        @input="handleInput"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted, nextTick, computed } from 'vue'
import { Search } from '@element-plus/icons-vue'

const props = defineProps({
  keyword: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['search', 'update:keyword'])

const localKeyword = ref(props.keyword)
const scrollDirection = ref(null) // 'up' | 'down' | null
const searchBoxHeight = ref(80) // 搜索框高度

// 简化的配置
const config = {
  scrollThreshold: 10       // 最小滚动距离才触发方向变化
}

// 滚动相关变量
let lastScrollY = 0
let ticking = false

// 计算搜索框是否应该隐藏
const shouldHide = computed(() => {
  return scrollDirection.value === 'down'
})

// 计算第一个卡片需要的 padding-top
const firstCardPaddingTop = computed(() => {
  // 当搜索框隐藏时，第一个卡片需要额外的 padding 来补偿空间
  return shouldHide.value ? 0 : searchBoxHeight.value
})

// 监听 keyword prop 变化
watch(() => props.keyword, (newValue) => {
  localKeyword.value = newValue
})

// 监听本地 keyword 变化
watch(localKeyword, (newValue) => {
  emit('update:keyword', newValue)
})

const handleSearch = () => {
  emit('search')
}

const handleInput = () => {
  // 可以在这里添加实时搜索逻辑
}

// 极简的滚动处理逻辑 - 只处理方向
const updateScrollDirection = () => {
  if (ticking) return

  ticking = true
  requestAnimationFrame(() => {
    const scrollY = window.pageYOffset

    // 确定滚动方向
    const direction = scrollY > lastScrollY ? "down" : "up"
    if (direction !== scrollDirection.value && Math.abs(scrollY - lastScrollY) > config.scrollThreshold) {
      scrollDirection.value = direction
    }

    lastScrollY = scrollY > 0 ? scrollY : 0
    ticking = false
  })
}

onMounted(() => {
  // 初始化状态
  lastScrollY = window.pageYOffset

  // 添加滚动监听
  window.addEventListener('scroll', updateScrollDirection, { passive: true })
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('scroll', updateScrollDirection)
})

// 暴露给父组件使用
defineExpose({
  firstCardPaddingTop
})
</script>

<style lang="scss" scoped>
.smart-search-box {
  // 始终固定在顶部，避免模式切换
  position: fixed;
  top: 64px; // AppHeader 高度
  left: 0;
  right: 0;
  z-index: $z-index-sticky - 1;
  height: 80px; // 固定高度
  background: rgba(245, 247, 250, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(235, 238, 245, 0.8);
  box-shadow: $shadow-2;
  padding: $space-xl 0;
  transition: transform 0.3s ease; // 只对 transform 做动画

  @include mobile {
    top: 56px; // 移动端 AppHeader 高度
  }

  // 隐藏状态 - 向上移动整个高度
  &.is-hidden {
    transform: translateY(-100%);
  }
  
  .search-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 $space-lg;

    @media (max-width: #{$breakpoint-mobile - 1px}) {
      padding: 0 $space-md;
    }
  }

  .el-input {
    // 移除 max-width 限制，让搜索框与容器宽度一致
    width: 100%;
    margin: 0;
    
    :deep(.el-input__wrapper) {
      border-radius: $radius-lg;
      box-shadow: $shadow-1;
      border: 1px solid $border-light;
      transition: all $transition-base;
      background: $bg-primary;
      
      &:hover {
        border-color: $primary-color;
        box-shadow: $shadow-2;
      }
      
      &.is-focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 3px $primary-light;
      }
    }
  }
}

// 暗色模式适配
@include dark-mode {
  .smart-search-box {
    background: rgba(31, 34, 37, 0.9);

    .el-input :deep(.el-input__wrapper) {
      background: $dark-bg-primary;
      border-color: $dark-border-light;

      &:hover {
        border-color: $primary-color;
      }
    }
  }
}
</style>