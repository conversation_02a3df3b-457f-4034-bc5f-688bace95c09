class AuthService {
  // 令牌管理 - 只处理后端返回的认证令牌
  setToken(token) {
    try {
      if (token) {
        localStorage.setItem('auth_token', token)
      }
    } catch (error) {
      console.error('Failed to save auth token:', error)
    }
  }

  getToken() {
    try {
      return localStorage.getItem('auth_token')
    } catch (error) {
      console.error('Failed to get auth token:', error)
      return null
    }
  }

  setUserInfo(userInfo) {
    try {
      if (userInfo) {
        localStorage.setItem('user_info', JSON.stringify(userInfo))
      }
    } catch (error) {
      console.error('Failed to save user info:', error)
    }
  }

  getUserInfo() {
    try {
      const userInfo = localStorage.getItem('user_info')
      return userInfo ? JSON.parse(userInfo) : null
    } catch (error) {
      console.error('Failed to get user info:', error)
      return null
    }
  }

  clearAuth() {
    try {
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_info')
    } catch (error) {
      console.error('Failed to clear auth data:', error)
    }
  }

  // 检查是否已认证 - 检查是否有有效的令牌
  isAuthenticated() {
    const token = this.getToken()
    return !!(token && token.trim())
  }

  // 设置认证状态 - 从后端回调中获取令牌和用户信息
  setAuthenticated(token, userInfo) {
    if (!token || !token.trim()) {
      console.warn('Invalid auth token provided')
      return false
    }
    
    this.setToken(token)
    if (userInfo) {
      this.setUserInfo(userInfo)
    }
    return true
  }

  // 登出 - 清理本地状态
  logout() {
    this.clearAuth()
  }

  // 获取认证头部，用于 API 请求
  getAuthHeader() {
    const token = this.getToken()
    return token ? { 'Authorization': `Bearer ${token}` } : {}
  }
}

export const authService = new AuthService()