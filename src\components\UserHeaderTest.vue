<template>
  <div class="test-container">
    <h2>UserHeader 组件样式测试</h2>
    
    <div class="test-cases">
      <!-- 测试用例1：普通用户 -->
      <div class="test-case">
        <h3>普通用户</h3>
        <div class="card-wrapper">
          <UserHeader 
            :user="normalUser"
            :created-at="testDate"
            :source="testSource"
            :region-name="testRegion"
          />
        </div>
      </div>
      
      <!-- 测试用例2：认证用户 -->
      <div class="test-case">
        <h3>认证用户（有粉丝数）</h3>
        <div class="card-wrapper">
          <UserHeader 
            :user="verifiedUser"
            :created-at="testDate"
            :source="testSource"
            :region-name="testRegion"
          />
        </div>
      </div>
      
      <!-- 测试用例3：大V用户 -->
      <div class="test-case">
        <h3>大V用户（高粉丝数）</h3>
        <div class="card-wrapper">
          <UserHeader 
            :user="bigVUser"
            :created-at="testDate"
            :source="testSource"
            :region-name="testRegion"
          />
        </div>
      </div>
      
      <!-- 测试用例4：长用户名 -->
      <div class="test-case">
        <h3>长用户名测试</h3>
        <div class="card-wrapper">
          <UserHeader 
            :user="longNameUser"
            :created-at="testDate"
            :source="testSource"
            :region-name="testRegion"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import UserHeader from './UserHeader.vue'

export default {
  name: 'UserHeaderTest',
  components: {
    UserHeader
  },
  data() {
    return {
      testDate: new Date().toISOString(),
      testSource: '<a href="#">微博网页版</a>',
      testRegion: '北京',
      
      normalUser: {
        id: '1',
        screen_name: '普通用户',
        profile_image_url: 'https://via.placeholder.com/50x50/4096ff/ffffff?text=U1',
        verified: false,
        followers_count: 123
      },
      
      verifiedUser: {
        id: '2',
        screen_name: '认证用户',
        profile_image_url: 'https://via.placeholder.com/50x50/1677ff/ffffff?text=V1',
        verified: true,
        followers_count: 5678
      },
      
      bigVUser: {
        id: '3',
        screen_name: '大V用户',
        profile_image_url: 'https://via.placeholder.com/50x50/ff6b35/ffffff?text=BV',
        verified: true,
        followers_count: 1234567
      },
      
      longNameUser: {
        id: '4',
        screen_name: '这是一个非常非常长的用户名测试用例看看会不会溢出',
        profile_image_url: 'https://via.placeholder.com/50x50/67c23a/ffffff?text=LN',
        verified: false,
        followers_count: 999
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.test-container {
  max-width: 800px;
  margin: 0 auto;
  padding: $space-xl;
  
  h2 {
    text-align: center;
    color: $text-primary;
    margin-bottom: $space-2xl;
  }
}

.test-cases {
  display: flex;
  flex-direction: column;
  gap: $space-2xl;
}

.test-case {
  h3 {
    color: $text-secondary;
    margin-bottom: $space-lg;
    font-size: $font-size-lg;
  }
}

.card-wrapper {
  @include card-base;
  padding: $space-xl;
  background: $bg-primary;
  border-radius: $radius-xl;
  box-shadow: $shadow-2;
  
  &:hover {
    @include card-hover;
  }
}

// 暗色模式适配
@include dark-mode {
  .test-container h2 {
    color: $dark-text-primary;
  }
  
  .test-case h3 {
    color: $dark-text-secondary;
  }
  
  .card-wrapper {
    background: $dark-bg-primary;
    border-color: $dark-border-light;
  }
}
</style>
