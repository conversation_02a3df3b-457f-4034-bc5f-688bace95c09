<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片预览测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    <style>
        .test-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .image-grid {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .el-image {
            width: 150px;
            height: 150px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h2>Element Plus 图片预览测试</h2>
            <div class="image-grid">
                <el-image 
                    v-for="(url, index) in testImages" 
                    :key="index"
                    :src="url" 
                    fit="cover"
                    :preview-src-list="testImages"
                    :initial-index="index"
                    preview-teleported
                    @click="handleImageClick(index)"
                >
                    <template #error>
                        <div>加载失败</div>
                    </template>
                </el-image>
            </div>
            <p>点击图片应该能够打开全屏预览</p>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3.3.8/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    testImages: [
                        'https://picsum.photos/400/400?random=1',
                        'https://picsum.photos/400/400?random=2',
                        'https://picsum.photos/400/400?random=3',
                        'https://picsum.photos/400/400?random=4'
                    ]
                }
            },
            methods: {
                handleImageClick(index) {
                    console.log('点击了图片:', index);
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>