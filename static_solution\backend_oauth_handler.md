# 后端 OAuth 处理方案

## 架构说明

这个方案将 OAuth2 认证逻辑完全移到后端处理，前端保持纯静态页面。

## 后端需要实现的接口

### 1. OAuth 启动接口

```
GET /api/oauth/start?backto={前端页面URL}
```

**功能：**
- 生成 OAuth2 授权 URL
- 重定向到微信 OAuth 页面
- 保存 backto 参数用于认证成功后跳转

**实现示例（Node.js/Express）：**

```javascript
app.get('/api/oauth/start', (req, res) => {
    const backto = req.query.backto;
    const state = generateRandomState(); // 生成随机 state
    
    // 保存状态到 session 或 Redis
    req.session.oauth_state = state;
    req.session.backto = backto;
    
    const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?` +
        `appid=${WECHAT_APPID}&` +
        `redirect_uri=${encodeURIComponent(OAUTH_CALLBACK_URL)}&` +
        `response_type=code&` +
        `scope=snsapi_base&` +
        `state=${state}&` +
        `agentid=${WECHAT_AGENTID}#wechat_redirect`;
    
    res.redirect(authUrl);
});
```

### 2. OAuth 回调处理

```
GET /api/oauth/callback?code={授权码}&state={状态参数}
```

**功能：**
- 验证 state 参数
- 使用授权码交换访问令牌
- 获取用户信息
- 生成前端认证令牌
- 跳转回前端页面

**实现示例：**

```javascript
app.get('/api/oauth/callback', async (req, res) => {
    const { code, state } = req.query;
    
    // 验证 state
    if (state !== req.session.oauth_state) {
        return res.redirect(`${req.session.backto}?auth_error=invalid_state`);
    }
    
    try {
        // 交换访问令牌
        const tokenResponse = await axios.post('https://qyapi.weixin.qq.com/cgi-bin/gettoken', {
            corpid: WECHAT_CORPID,
            corpsecret: WECHAT_SECRET
        });
        
        const accessToken = tokenResponse.data.access_token;
        
        // 获取用户信息
        const userResponse = await axios.get(`https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=${accessToken}&code=${code}`);
        
        const userInfo = userResponse.data;
        
        // 生成前端令牌（JWT 或简单的随机字符串）
        const frontendToken = generateFrontendToken(userInfo);
        
        // 存储用户信息和令牌的映射
        await storeUserSession(frontendToken, userInfo);
        
        // 跳转回前端
        const backUrl = new URL(req.session.backto);
        backUrl.searchParams.set('token', frontendToken);
        backUrl.searchParams.set('user', encodeURIComponent(JSON.stringify({
            name: userInfo.name,
            userid: userInfo.userid
        })));
        backUrl.searchParams.set('auth_success', 'true');
        
        res.redirect(backUrl.toString());
        
    } catch (error) {
        console.error('OAuth callback error:', error);
        res.redirect(`${req.session.backto}?auth_error=oauth_failed`);
    }
});
```

### 3. API 数据接口

```
GET /api/tweets?user={用户ID}&limit={数量}&keyword={关键词}
Authorization: Bearer {前端令牌}
```

**功能：**
- 验证前端令牌
- 调用微博 API 获取数据
- 返回处理后的数据

**实现示例：**

```javascript
app.get('/api/tweets', authenticateToken, async (req, res) => {
    try {
        const { user, limit = 10, keyword } = req.query;
        
        // 调用微博 API（使用服务器端的访问令牌）
        const weiboData = await fetchWeiboData({
            user,
            limit,
            keyword,
            accessToken: req.user.weiboAccessToken
        });
        
        res.json({
            success: true,
            data: weiboData
        });
        
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// 令牌验证中间件
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }
    
    // 验证令牌并获取用户信息
    const userInfo = getUserByToken(token);
    if (!userInfo) {
        return res.status(403).json({ error: 'Invalid token' });
    }
    
    req.user = userInfo;
    next();
}
```

## 部署方案

### 方案 A：混合部署
```
静态文件服务器 (Nginx/CDN)
├── index.html (前端静态页面)
├── assets/ (CSS, JS, 图片等)

后端 API 服务器 (Node.js/Python/Go)
├── /api/oauth/start
├── /api/oauth/callback  
├── /api/tweets
└── /api/other-endpoints
```

### 方案 B：单服务器部署
```
Web 服务器 (Express/Flask/Gin)
├── /static/ (静态文件)
│   └── index.html
├── /api/oauth/start
├── /api/oauth/callback
└── /api/tweets
```

## 安全考虑

1. **HTTPS 必须**：OAuth2 要求使用 HTTPS
2. **State 参数验证**：防止 CSRF 攻击
3. **令牌管理**：
   - 前端令牌应该有过期时间
   - 服务器端安全存储真实的访问令牌
   - 支持令牌刷新机制
4. **CORS 配置**：如果前后端分离部署
5. **Rate Limiting**：防止 API 滥用

## 优势

1. ✅ **安全性高**：敏感信息不暴露给前端
2. ✅ **真正的静态部署**：前端可以部署到 CDN
3. ✅ **SEO 友好**：纯 HTML 页面
4. ✅ **缓存优化**：静态资源可以长期缓存
5. ✅ **扩展性好**：后端可以处理复杂的业务逻辑

## 劣势

1. ❌ **需要后端服务**：不能纯前端部署
2. ❌ **复杂度增加**：需要维护前后端两套代码
3. ❌ **跨域问题**：如果前后端不同域名

## 总结

这个方案是静态页面 + OAuth2 认证的最佳实践，既保持了前端的静态特性，又安全地处理了认证流程。