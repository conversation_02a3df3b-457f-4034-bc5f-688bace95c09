/**
 * OAuth 认证流程测试脚本
 * 用于验证 History 模式下的认证配置
 */

// 模拟认证回调 URL 参数
const testAuthCallback = () => {
  console.log('🧪 Testing OAuth callback handling...')
  
  // 测试成功回调
  const successUrl = new URL(window.location.origin + '/auth/callback')
  successUrl.searchParams.set('token', 'test_token_123')
  successUrl.searchParams.set('user', encodeURIComponent(JSON.stringify({
    id: '123',
    name: 'Test User',
    avatar: 'https://example.com/avatar.jpg'
  })))
  
  console.log('✅ Success callback URL:', successUrl.toString())
  
  // 测试错误回调
  const errorUrl = new URL(window.location.origin + '/auth/callback')
  errorUrl.searchParams.set('auth_error', 'access_denied')
  
  console.log('❌ Error callback URL:', errorUrl.toString())
  
  // 测试 URL 清理
  const cleanUrl = window.location.origin + '/auth/callback'
  console.log('🧹 Clean URL after processing:', cleanUrl)
}

// 测试认证流程 URL 构建
const testAuthFlow = () => {
  console.log('🧪 Testing OAuth flow URL construction...')
  
  const callbackUrl = window.location.origin + '/auth/callback'
  const backUrl = encodeURIComponent(callbackUrl)
  const apiBase = 'https://weibo.api.maikebuke.com'
  const authUrl = `${apiBase}/api/oauth2?backto=${backUrl}`
  const jumpUrl = `${apiBase}/api/oauth2?redirect=${encodeURIComponent(authUrl)}`
  
  console.log('🔗 Callback URL:', callbackUrl)
  console.log('🔗 Auth URL:', authUrl)
  console.log('🔗 Jump URL:', jumpUrl)
}

// 在开发环境中运行测试
if (import.meta.env.VITE_APP_ENV === 'development') {
  console.log('🚀 Running OAuth tests in development mode...')
  testAuthCallback()
  testAuthFlow()
}

export { testAuthCallback, testAuthFlow }