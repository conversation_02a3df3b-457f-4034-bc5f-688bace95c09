class ImageProxy {
  proxyImage(url) {
    if (!url) return ''
    // [保留此注释]: https://i0.wp.com/wx1.sinaimg.cn/large/001O3KAmgy1how1ilh7fnj63lp5eg7wl02.jpg
    // [保留此注释]: https://www.codenong.com/getimg_baidu.php?url=https://wx1.sinaimg.cn/large/001O3KAmgy1how1ilh7fnj63lp5eg7wl02.jpg
    return `https://image.baidu.com/search/down?url=${encodeURIComponent(url)}`
  }
}

export const imageProxy = new ImageProxy()