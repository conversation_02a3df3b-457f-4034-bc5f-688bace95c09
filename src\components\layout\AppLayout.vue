<template>
  <div class="app-layout">
    <!-- 顶部导航栏 -->
    <AppHeader />
    
    <!-- 主内容区域 -->
    <main class="app-main">
      <!-- 页面加载状态 -->
      <div v-if="isLoading" class="app-loading">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
      </div>
      
      <!-- 路由视图 -->
      <router-view v-else v-slot="{ Component, route }">
        <transition name="page" mode="out-in">
          <component :is="Component" :key="route.path" />
        </transition>
      </router-view>
    </main>
  </div>
</template>

<script>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import AppHeader from './AppHeader.vue'

export default {
  name: 'AppLayout',
  components: {
    AppHeader
  },
  setup() {
    const router = useRouter()
    const isLoading = ref(false)
    
    // 监听路由变化，显示加载状态
    watch(() => router.currentRoute.value, (to, from) => {
      if (to.path !== from?.path) {
        isLoading.value = true
        
        // 模拟页面加载时间
        setTimeout(() => {
          isLoading.value = false
        }, 300)
      }
    })
    
    onMounted(() => {
      // 初始化完成
      isLoading.value = false
    })
    
    return {
      isLoading
    }
  }
}
</script>

<style lang="scss" scoped>
.app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, $bg-secondary 0%, $bg-tertiary 100%);
}

.app-main {
  flex: 1;
  position: relative;
  overflow-x: hidden;
}

// 页面加载状态
.app-loading {
  @include flex-center;
  @include flex-column;
  min-height: 60vh;
  gap: $space-lg;
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid $border-light;
    border-radius: 50%;
    border-top-color: $primary-color;
    animation: spin 1s linear infinite;
  }
  
  p {
    color: $text-secondary;
    font-size: $font-size-base;
    margin: 0;
  }
}

// 页面切换动画
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease-out;
}

.page-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

// 响应式适配
@include mobile {
  .app-main {
    padding-top: 0;
  }
}

// 动画定义
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>