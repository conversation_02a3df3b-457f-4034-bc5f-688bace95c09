# 环境配置说明

本项目支持多环境配置，包括开发、测试和生产环境。

## 环境文件

- `.env` - 默认环境配置
- `.env.development` - 开发环境配置
- `.env.test` - 测试环境配置  
- `.env.production` - 生产环境配置

## 环境变量

### VITE_API_BASE_URL
API 服务器的基础 URL

- 开发环境: `http://localhost:8080`
- 测试环境: `https://test-api.weibo.example.com`
- 生产环境: `https://api.weibo.example.com`

### VITE_APP_ENV
当前应用环境标识

### VITE_APP_TITLE
应用标题

## 使用方法

### 开发模式
```bash
# 开发环境
npm run dev

# 测试环境
npm run dev:test
```

### 构建模式
```bash
# 生产环境构建
npm run build

# 测试环境构建
npm run build:test

# 开发环境构建
npm run build:dev
```

## 代码中使用环境配置

```javascript
import { API_CONFIG, isDevelopment, isTest, isProduction } from '@/config/api.js'

// 获取 API 基础 URL
console.log(API_CONFIG.BASE_URL)

// 环境判断
if (isDevelopment()) {
  console.log('开发环境')
}

if (isTest()) {
  console.log('测试环境')
}

if (isProduction()) {
  console.log('生产环境')
}
```

## API 服务使用

```javascript
import { weiboService } from '@/services/weiboService.js'

// 获取微博数据
const tweets = await weiboService.getTweets({
  user: '123456',
  limit: 10,
  keyword: '搜索关键词'
})

// 获取用户信息
const userInfo = await weiboService.getUserInfo('123456')

// 搜索微博
const searchResults = await weiboService.searchTweets({
  keyword: '搜索关键词',
  limit: 20
})
```

## 注意事项

1. 环境变量必须以 `VITE_` 开头才能在客户端代码中访问
2. 修改环境文件后需要重启开发服务器
3. 生产环境的 API URL 需要根据实际部署情况修改
4. 可以通过 Vite 的代理功能解决开发环境的跨域问题