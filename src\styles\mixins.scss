// SCSS 混合系统 - 可复用样式模式

@use './variables.scss' as *;

// 卡片样式混合
@mixin card-base {
  background: $bg-primary;
  border-radius: $radius-xl;
  border: 1px solid $border-light;
  box-shadow: $shadow-1;
  transition: all $transition-base;
  overflow: hidden;
  position: relative;
}

@mixin card-hover {
  transform: translateY(-2px);
  box-shadow: $shadow-hover;
  border-color: $border-default;
  
  &::before {
    opacity: 1;
  }
}

@mixin card-active {
  transform: translateY(-1px) scale(0.995);
}

@mixin card-top-accent {
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, $primary-color, $primary-hover);
    opacity: 0;
    transition: opacity $transition-base;
  }
}

// 按钮样式混合
@mixin button-base {
  border-radius: $radius-lg;
  font-weight: 500;
  transition: all $transition-base;
  cursor: pointer;
  border: none;
  outline: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  gap: $space-xs;
  font-family: inherit;
  
  &:focus-visible {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
    transform: none !important;
  }
}

@mixin button-primary {
  background: $primary-color;
  color: white;
  border: 1px solid $primary-color;
  
  &:hover:not(:disabled) {
    background: $primary-hover;
    transform: translateY(-1px);
    box-shadow: $shadow-2;
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
  }
}

@mixin button-secondary {
  background: transparent;
  color: $primary-color;
  border: 1px solid $primary-color;
  
  &:hover:not(:disabled) {
    background: $primary-color;
    color: white;
    transform: translateY(-1px);
    box-shadow: $shadow-2;
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
  }
}

@mixin button-success {
  background: transparent;
  color: #67c23a;
  border: 1px solid #67c23a;
  
  &:hover:not(:disabled) {
    background: #67c23a;
    color: white;
    transform: translateY(-1px);
    box-shadow: $shadow-2;
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
  }
}

@mixin button-danger {
  background: transparent;
  color: #f56c6c;
  border: 1px solid #f56c6c;
  
  &:hover:not(:disabled) {
    background: #f56c6c;
    color: white;
    transform: translateY(-1px);
    box-shadow: $shadow-2;
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
  }
}

@mixin button-ghost {
  background: transparent;
  color: $text-secondary;
  border: 1px solid $border-default;
  
  &:hover:not(:disabled) {
    color: $primary-color;
    border-color: $primary-color;
    transform: translateY(-1px);
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
  }
}

@mixin button-text {
  background: transparent;
  color: $primary-color;
  border: none;
  
  &:hover:not(:disabled) {
    background: $primary-light;
    transform: translateY(-1px);
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
  }
}

// 按钮尺寸混合
@mixin button-small {
  padding: $space-xs $space-md;
  font-size: $font-size-sm;
  min-height: 28px;
}

@mixin button-medium {
  padding: $space-sm $space-lg;
  font-size: $font-size-base;
  min-height: 32px;
}

@mixin button-large {
  padding: $space-md $space-xl;
  font-size: $font-size-lg;
  min-height: 40px;
}

// 按钮形状混合
@mixin button-round {
  border-radius: 50px;
}

@mixin button-circle {
  border-radius: 50%;
  width: 32px;
  height: 32px;
  padding: 0;
  
  &.button-small {
    width: 28px;
    height: 28px;
  }
  
  &.button-large {
    width: 40px;
    height: 40px;
  }
}

// 输入框样式混合
@mixin input-base {
  border-radius: $radius-lg;
  border: 1px solid $border-light;
  background: $bg-primary;
  transition: all $transition-base;
  font-family: $font-family-base;
  
  &:hover {
    border-color: $primary-color;
  }
  
  &:focus {
    border-color: $primary-color;
    box-shadow: 0 0 0 3px $primary-light;
    outline: none;
  }
  
  &::placeholder {
    color: $text-tertiary;
  }
}

// 响应式混合
@mixin mobile {
  @media (max-width: #{$breakpoint-mobile - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: $breakpoint-mobile) and (max-width: #{$breakpoint-tablet - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: $breakpoint-tablet) {
    @content;
  }
}

@mixin mobile-up {
  @media (min-width: $breakpoint-mobile) {
    @content;
  }
}

@mixin tablet-up {
  @media (min-width: $breakpoint-tablet) {
    @content;
  }
}

// 文字样式混合
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-clamp($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@mixin heading-base {
  font-weight: 600;
  line-height: $line-height-tight;
  color: $text-primary;
  margin: 0;
}

@mixin body-text {
  font-size: $font-size-base;
  line-height: $line-height-base;
  color: $text-primary;
}

@mixin small-text {
  font-size: $font-size-sm;
  line-height: $line-height-base;
  color: $text-secondary;
}

// 布局混合
@mixin container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 $space-lg;
  position: relative;
  
  @media (max-width: #{$breakpoint-mobile - 1px}) {
    padding: 0 $space-md;
  }
}

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

// 动画混合
@mixin fade-in($duration: $transition-base) {
  animation: fadeIn $duration ease-out;
}

@mixin fade-in-up($duration: 0.6s) {
  animation: fadeInUp $duration ease-out;
}

@mixin slide-in-right($duration: $transition-base) {
  animation: slideInRight $duration ease-out;
}

// 滚动条样式混合
@mixin custom-scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: $bg-tertiary;
    border-radius: $radius-sm;
  }
  
  &::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, $primary-color, $primary-hover);
    border-radius: $radius-sm;
    transition: all $transition-base;
    
    &:hover {
      background: linear-gradient(180deg, $primary-hover, $primary-color);
    }
  }
}

// 阴影混合
@mixin elevation-1 {
  box-shadow: $shadow-1;
}

@mixin elevation-2 {
  box-shadow: $shadow-2;
}

@mixin elevation-3 {
  box-shadow: $shadow-3;
}

@mixin elevation-hover {
  box-shadow: $shadow-hover;
}

// 暗色模式混合
@mixin dark-mode {
  @media (prefers-color-scheme: dark) {
    @content;
  }
}

// 高对比度模式混合
@mixin high-contrast {
  @media (prefers-contrast: high) {
    @content;
  }
}

// 减少动画模式混合
@mixin reduced-motion {
  @media (prefers-reduced-motion: reduce) {
    @content;
  }
}

// 打印样式混合
@mixin print-hidden {
  @media print {
    display: none !important;
  }
}

@mixin print-only {
  display: none;
  
  @media print {
    display: block;
  }
}

// 可访问性混合
@mixin sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

@mixin focus-visible {
  &:focus-visible {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
    border-radius: $radius-sm;
    z-index: 1;
  }
  
  &:focus:not(:focus-visible) {
    outline: none;
  }
}

// 键盘导航混合
@mixin keyboard-navigable {
  &:focus-visible {
    @include focus-visible;
  }
  
  // 键盘激活
  &:focus:active {
    transform: scale(0.98);
  }
}

// 触摸友好混合
@mixin touch-friendly {
  min-height: 44px;
  min-width: 44px;
  
  @include mobile {
    min-height: 48px;
    min-width: 48px;
  }
}