<template>
  <el-card 
    class="box-card" 
    :class="{ 'clickable-card': clickable }"
    @click="handleCardClick"
  >
    <!-- 头部 -->
    <template #header>
      <UserHeader 
        :user="card.user"
        :created-at="card.created_at"
        :source="card.source"
        :region-name="card.region_name"
      />
    </template>

    <!-- 正文 -->
    <div class="card-content" v-html="processedText"></div>

    <!-- 图片 -->
    <ImageGrid 
      :images="mainImages" 
      key-prefix="main"
    />

    <!-- 转发内容 -->
    <RetweetCard :retweeted-status="card.retweeted_status" />

    <!-- 操作按钮 -->
    <div v-if="showActions" class="card-actions">
      <el-button 
        v-if="showUserProfile" 
        type="success" 
        plain 
        round 
        size="small" 
        @click="viewUserProfile"
      >
        <el-icon><User /></el-icon>查看用户历史
      </el-button>
      <el-button type="primary" plain round size="small" @click="openWeibo">
        <el-icon><Link /></el-icon>查看原文
      </el-button>
    </div>
  </el-card>
</template>

<script>
import { computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Link, User } from '@element-plus/icons-vue'
import { textFormatter } from '../utils/textFormatter'
import { extractImageUrls } from '../utils/imageUtils'
import UserHeader from './UserHeader.vue'
import ImageGrid from './ImageGrid.vue'
import RetweetCard from './RetweetCard.vue'

export default {
  name: 'WeiboCard',
  components: {
    Link,
    User,
    UserHeader,
    ImageGrid,
    RetweetCard
  },
  props: {
    card: {
      type: Object,
      required: true
    },
    isVisible: {
      type: Boolean,
      default: false
    },
    showActions: {
      type: Boolean,
      default: true
    },
    clickable: {
      type: Boolean,
      default: false
    },
    showUserProfile: {
      type: Boolean,
      default: false
    }
  },
  emits: ['card-click'],
  setup(props, { emit }) {
    const router = useRouter()
    
    // 处理后的正文
    const processedText = computed(() => {
      return textFormatter.formatText(props.card.text)
    })

    // 微博链接
    const weiboUrl = computed(() => {
      return `https://m.weibo.cn/detail/${props.card.id}`
    })

    // 主图列表
    const mainImages = computed(() => {
      return extractImageUrls(props.card)
    })

    const openWeibo = () => {
      window.open(weiboUrl.value, '_blank')
    }

    const viewUserProfile = () => {
      if (props.card?.user?.id) {
        router.push(`/user/${props.card.user.id}`)
      }
    }

    const handleCardClick = (event) => {
      // 检查点击的是否是图片、链接或按钮等交互元素
      const target = event.target
      const isInteractiveElement = target.closest('.el-image, .el-button, a, button, .image-grid, .card-actions')
      
      if (!isInteractiveElement && props.clickable) {
        // 只有在不是交互元素时才触发卡片点击事件
        emit('card-click', props.card)
      }
    }

    watch(() => props.isVisible, (newValue) => {
      if (newValue) {
        //console.log('Card is visible:', props.card.id)
      }
    })

    return {
      processedText,
      mainImages,
      openWeibo,
      viewUserProfile,
      handleCardClick
    }
  }
}
</script>

<style lang="scss" scoped>
.clickable-card {
  cursor: pointer;
  transition: all $transition-base;
  
  &:hover {
    @include card-hover;
  }
  
  &:active {
    @include card-active;
  }
}

// 确保卡片使用全局样式
:deep(.box-card) {
  @include card-base;
  @include card-top-accent;
  
  &:hover {
    @include card-hover;
  }
  
  &:active {
    @include card-active;
  }
  
  .el-card__header {
    padding: $space-xl;
    border-bottom: 1px solid $border-light;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.8) 100%);
    
    @include mobile {
      padding: $space-lg;
    }
  }
  
  .el-card__body {
    padding: 0;
  }
}

// 卡片内容样式
:deep(.card-content) {
  padding: $space-xl;
  line-height: $line-height-relaxed;
  color: $text-primary;
  font-size: $font-size-base;
  word-break: break-word;
  
  @include mobile {
    padding: $space-lg;
    font-size: $font-size-sm;
  }
  
  .inline-emoji {
    display: inline-block;
    vertical-align: middle;
    height: 1.2em;
    width: auto;
    margin: 0 2px;
    transition: transform $transition-fast;
    
    &:hover {
      transform: scale(1.2);
    }
  }
  
  a {
    color: $primary-color;
    text-decoration: none;
    font-weight: 500;
    transition: all $transition-fast;
    border-radius: $radius-sm;
    padding: 2px 4px;
    margin: 0 1px;
    
    &:hover {
      color: $primary-hover;
      background: $primary-light;
      transform: translateY(-1px);
    }
  }
}

// 卡片操作按钮样式
:deep(.card-actions) {
  padding: $space-lg $space-xl;
  border-top: 1px solid $border-light;
  text-align: right;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(248, 250, 252, 0.3) 100%);
  
  @include mobile {
    padding: $space-md $space-lg;
  }
  
  .el-button {
    @include button-base;
    margin-left: $space-sm;
    
    &.el-button--success {
      @include button-secondary;
      color: #67c23a;
      border-color: #67c23a;
      
      &:hover {
        background: #67c23a;
        color: white;
      }
    }
    
    &.el-button--primary {
      @include button-primary;
    }
    
    &[plain] {
      background: transparent;
    }
    
    &[round] {
      border-radius: 50px;
    }
    
    &[size="small"] {
      padding: $space-xs $space-md;
      font-size: $font-size-sm;
    }
  }
}

// 暗色模式适配
@include dark-mode {
  :deep(.box-card) {
    background: $dark-bg-primary;
    border-color: $dark-border-light;
    
    .el-card__header {
      background: linear-gradient(135deg, rgba(36, 37, 38, 0.8) 0%, rgba(58, 59, 60, 0.6) 100%);
      border-color: $dark-border-light;
    }
  }
  
  :deep(.card-content) {
    color: $dark-text-primary;
    
    a {
      color: $primary-color;
      
      &:hover {
        background: rgba(22, 119, 255, 0.2);
      }
    }
  }
  
  :deep(.card-actions) {
    background: linear-gradient(135deg, rgba(36, 37, 38, 0.5) 0%, rgba(58, 59, 60, 0.3) 100%);
    border-color: $dark-border-light;
  }
}
</style>