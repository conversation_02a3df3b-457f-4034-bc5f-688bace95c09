# Vercel 部署指南

## 部署步骤

### 1. 准备工作
确保项目已经从 Hash 路由模式切换到 History 路由模式。

### 2. 环境变量配置
在 Vercel 项目设置中配置以下环境变量：

```
VITE_API_BASE_URL=https://your-api-domain.com
VITE_APP_ENV=production
VITE_APP_TITLE=微博查看器
```

### 3. 部署配置文件
项目包含以下 Vercel 配置文件：

- `vercel.json`: 主要的 Vercel 配置文件
- `public/_redirects`: 备用重定向配置

### 4. 构建命令
Vercel 会自动使用 `vercel-build` 脚本进行构建。

### 5. 路由配置
- 已从 Hash 模式 (`createWebHashHistory`) 切换到 History 模式 (`createWebHistory`)
- 配置了 SPA 路由重定向，所有路由都会指向 `index.html`

### 6. 性能优化
- 启用了代码分割 (code splitting)
- 配置了静态资源缓存
- 添加了安全头部

### 7. OAuth 回调配置
在 History 模式下，OAuth 回调使用专门的路由：
- 回调 URL: `https://your-domain.com/auth/callback`
- 确保后端 OAuth 配置中的回调 URL 指向此路径

### 8. 部署验证
部署后请验证以下功能：
- [ ] 首页正常加载
- [ ] 路由跳转正常（无 # 符号）
- [ ] 直接访问子路由正常（如 `/user/123`）
- [ ] 刷新页面不会出现 404 错误
- [ ] OAuth 认证流程正常（使用 `/auth/callback` 路由）
- [ ] 认证回调页面正常显示和跳转

## 常见问题

### Q: 刷新页面出现 404 错误
A: 确保 `vercel.json` 中的路由配置正确，所有路由都重定向到 `/index.html`。

### Q: 静态资源加载失败
A: 检查 `vite.config.js` 中的 `base` 配置，确保资源路径正确。

### Q: API 请求失败 (CORS/CSP 错误)
A: 
- 检查环境变量 `VITE_API_BASE_URL` 是否正确配置
- 确保 `vercel.json` 中的 CSP 头部包含了 API 域名
- 开发环境使用 Vite 代理避免 CORS 问题

### Q: Content Security Policy 错误
A: 
- 生产环境的 CSP 在 `vercel.json` 中配置
- 开发环境通过 Vite 代理解决跨域问题
- 确保 CSP 中的 `connect-src` 包含所有需要的 API 域名