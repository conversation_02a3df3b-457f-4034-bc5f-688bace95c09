// 组件样式 - 通用组件样式定义

@use './variables.scss' as *;
@use './mixins.scss' as *;

// 容器样式
.container {
  @include container;
}

// 卡片样式
.card {
  @include card-base;
  @include card-top-accent;
  
  &:hover {
    @include card-hover;
  }
  
  &:active {
    @include card-active;
  }
  
  &__header {
    padding: $space-xl;
    border-bottom: 1px solid $border-light;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.8) 100%);
  }
  
  &__body {
    padding: $space-xl;
  }
  
  &__footer {
    padding: $space-lg $space-xl;
    border-top: 1px solid $border-light;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(248, 250, 252, 0.3) 100%);
  }
}

// 按钮样式
.btn {
  @include button-base;
  @include button-medium;
  
  &--primary {
    @include button-primary;
  }
  
  &--secondary {
    @include button-secondary;
  }
  
  &--success {
    @include button-success;
  }
  
  &--danger {
    @include button-danger;
  }
  
  &--ghost {
    @include button-ghost;
  }
  
  &--text {
    @include button-text;
  }
  
  &--small {
    @include button-small;
  }
  
  &--large {
    @include button-large;
  }
  
  &--round {
    @include button-round;
  }
  
  &--circle {
    @include button-circle;
  }
  
  &--block {
    width: 100%;
    display: flex;
  }
}

// Element Plus 按钮样式统一
.el-button {
  @include button-base;
  @include button-medium;
  
  &.el-button--primary {
    @include button-primary;
    
    &.is-plain {
      @include button-secondary;
    }
  }
  
  &.el-button--success {
    @include button-success;
    
    &.is-plain {
      @include button-success;
    }
  }
  
  &.el-button--danger {
    @include button-danger;
    
    &.is-plain {
      @include button-danger;
    }
  }
  
  &.el-button--info {
    @include button-ghost;
  }
  
  &.el-button--text {
    @include button-text;
  }
  
  &.el-button--small {
    @include button-small;
  }
  
  &.el-button--large {
    @include button-large;
  }
  
  &.is-round {
    @include button-round;
  }
  
  &.is-circle {
    @include button-circle;
  }
  
  &.is-loading {
    pointer-events: none;
  }
}

// 输入框样式
.input {
  @include input-base;
  padding: $space-md;
  font-size: $font-size-base;
  width: 100%;
  
  &--small {
    padding: $space-sm;
    font-size: $font-size-sm;
  }
  
  &--large {
    padding: $space-lg;
    font-size: $font-size-lg;
  }
}

// Element Plus 输入框样式统一
.el-input {
  width: 100%;
  
  .el-input__wrapper {
    @include input-base;
    padding: $space-sm $space-md;
    font-size: $font-size-base;
    min-height: 32px;
    
    &:hover {
      border-color: $primary-color;
      box-shadow: $shadow-1;
    }
    
    &.is-focus {
      border-color: $primary-color;
      box-shadow: 0 0 0 3px $primary-light;
    }
    
    &.is-disabled {
      background-color: $bg-tertiary;
      color: $text-disabled;
      cursor: not-allowed;
    }
  }
  
  .el-input__inner {
    color: $text-primary;
    font-size: inherit;
    
    &::placeholder {
      color: $text-tertiary;
    }
  }
  
  .el-input__prefix,
  .el-input__suffix {
    color: $text-tertiary;
    
    .el-icon {
      font-size: $font-size-base;
    }
  }
  
  .el-input__prefix {
    left: $space-md;
  }
  
  .el-input__suffix {
    right: $space-md;
  }
  
  // 输入框尺寸
  &.el-input--small {
    .el-input__wrapper {
      padding: $space-xs $space-sm;
      font-size: $font-size-sm;
      min-height: 28px;
    }
  }
  
  &.el-input--large {
    .el-input__wrapper {
      padding: $space-md $space-lg;
      font-size: $font-size-lg;
      min-height: 40px;
    }
  }
}

// 搜索框特殊样式
.search-input {
  .el-input__wrapper {
    border-radius: $radius-xl;
    box-shadow: $shadow-2;
    
    &:hover {
      box-shadow: $shadow-3;
    }
    
    &.is-focus {
      box-shadow: 0 0 0 3px $primary-light, $shadow-2;
    }
  }
}

// 头像样式
.avatar {
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid $border-light;
  transition: all $transition-base;
  
  &:hover {
    border-color: $primary-color;
    transform: scale(1.05);
  }
  
  &--small {
    width: 32px;
    height: 32px;
  }
  
  &--medium {
    width: 40px;
    height: 40px;
  }
  
  &--large {
    width: 56px;
    height: 56px;
  }
}

// 标签样式
.tag {
  display: inline-block;
  padding: 2px 6px;
  border-radius: $radius-sm;
  font-size: $font-size-xs;
  font-weight: 500;
  transition: all $transition-fast;
  margin: 0 2px;
  
  &--primary {
    color: $primary-color;
    background: $primary-light;
    
    &:hover {
      background: $primary-color;
      color: white;
      transform: translateY(-1px);
    }
  }
  
  &--secondary {
    color: $text-secondary;
    background: $bg-tertiary;
    
    &:hover {
      color: $text-primary;
      background: $border-default;
    }
  }
}

// 加载动画
.loading {
  &__spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid $border-light;
    border-radius: 50%;
    border-top-color: $primary-color;
    animation: spin 1s ease-in-out infinite;
    
    &--small {
      width: 16px;
      height: 16px;
      border-width: 1.5px;
    }
    
    &--large {
      width: 32px;
      height: 32px;
      border-width: 3px;
    }
  }
  
  &__skeleton {
    background: linear-gradient(90deg, $bg-tertiary 25%, $bg-secondary 50%, $bg-tertiary 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: $radius-md;
  }
  
  &__dots {
    display: inline-flex;
    gap: $space-xs;
    
    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: $primary-color;
      animation: loading-dots 1.4s infinite ease-in-out;
      
      &:nth-child(1) { animation-delay: -0.32s; }
      &:nth-child(2) { animation-delay: -0.16s; }
      &:nth-child(3) { animation-delay: 0s; }
    }
  }
  
  &__pulse {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: $primary-color;
    border-radius: 50%;
    animation: loading-pulse 1.5s ease-in-out infinite;
  }
}

// Element Plus 加载组件样式统一
.el-loading-mask {
  background-color: rgba($bg-overlay, 0.8) !important;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  
  .el-loading-spinner {
    .circular {
      width: 42px;
      height: 42px;
      
      .path {
        stroke: $primary-color;
        stroke-width: 2;
      }
    }
    
    .el-loading-text {
      color: $text-primary;
      font-size: $font-size-base;
      margin-top: $space-md;
    }
  }
}

// 骨架屏样式优化
.el-skeleton {
  .el-skeleton__item {
    background: linear-gradient(90deg, $bg-tertiary 25%, $bg-secondary 50%, $bg-tertiary 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    
    &.el-skeleton__avatar {
      border-radius: 50%;
    }
    
    &.el-skeleton__button {
      border-radius: $radius-md;
    }
    
    &.el-skeleton__h1 {
      height: 24px;
    }
    
    &.el-skeleton__h3 {
      height: 18px;
    }
    
    &.el-skeleton__text {
      height: 14px;
      border-radius: $radius-sm;
    }
  }
}

// 空状态
.empty {
  text-align: center;
  padding: $space-2xl;
  color: $text-tertiary;
  
  &__icon {
    font-size: 48px;
    margin-bottom: $space-lg;
    opacity: 0.5;
  }
  
  &__text {
    font-size: $font-size-lg;
    margin-bottom: $space-md;
  }
  
  &__description {
    font-size: $font-size-sm;
    color: $text-tertiary;
  }
}

// Element Plus 空状态样式统一
.el-empty {
  padding: $space-2xl;
  
  .el-empty__image {
    width: 120px;
    margin-bottom: $space-xl;
    
    svg {
      fill: $text-tertiary;
      opacity: 0.6;
    }
  }
  
  .el-empty__description {
    color: $text-tertiary;
    font-size: $font-size-base;
    margin-bottom: $space-lg;
  }
}

// 消息提示样式
.message {
  &--success {
    color: #67c23a;
    background: rgba(103, 194, 58, 0.1);
    border: 1px solid rgba(103, 194, 58, 0.2);
  }
  
  &--warning {
    color: #e6a23c;
    background: rgba(230, 162, 60, 0.1);
    border: 1px solid rgba(230, 162, 60, 0.2);
  }
  
  &--error {
    color: #f56c6c;
    background: rgba(245, 108, 108, 0.1);
    border: 1px solid rgba(245, 108, 108, 0.2);
  }
  
  &--info {
    color: $primary-color;
    background: $primary-light;
    border: 1px solid rgba(22, 119, 255, 0.2);
  }
}

// Element Plus 消息组件样式统一
.el-message {
  border-radius: $radius-lg !important;
  box-shadow: $shadow-3 !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  
  &.el-message--success {
    background: rgba(103, 194, 58, 0.15) !important;
    border: 1px solid #67c23a !important;
    color: #67c23a !important;
    
    .el-message__icon {
      color: #67c23a !important;
    }
    
    .el-message__content {
      color: #67c23a !important;
    }
  }
  
  &.el-message--warning {
    background: rgba(230, 162, 60, 0.15) !important;
    border: 1px solid #e6a23c !important;
    color: #e6a23c !important;
    
    .el-message__icon {
      color: #e6a23c !important;
    }
    
    .el-message__content {
      color: #e6a23c !important;
    }
  }
  
  &.el-message--error {
    background: rgba(245, 108, 108, 0.15) !important;
    border: 1px solid #f56c6c !important;
    color: #f56c6c !important;
    
    .el-message__icon {
      color: #f56c6c !important;
    }
    
    .el-message__content {
      color: #f56c6c !important;
    }
  }
  
  &.el-message--info {
    background: rgba(22, 119, 255, 0.15) !important;
    border: 1px solid $primary-color !important;
    color: $primary-color !important;
    
    .el-message__icon {
      color: $primary-color !important;
    }
    
    .el-message__content {
      color: $primary-color !important;
    }
  }
}

// 警告框样式
.el-alert {
  border-radius: $radius-lg !important;
  border: none !important;
  
  &.el-alert--success {
    background: rgba(103, 194, 58, 0.1) !important;
    color: #67c23a !important;
    
    .el-alert__icon {
      color: #67c23a !important;
    }
  }
  
  &.el-alert--warning {
    background: rgba(230, 162, 60, 0.1) !important;
    color: #e6a23c !important;
    
    .el-alert__icon {
      color: #e6a23c !important;
    }
  }
  
  &.el-alert--error {
    background: rgba(245, 108, 108, 0.1) !important;
    color: #f56c6c !important;
    
    .el-alert__icon {
      color: #f56c6c !important;
    }
  }
  
  &.el-alert--info {
    background: $primary-light !important;
    color: $primary-color !important;
    
    .el-alert__icon {
      color: $primary-color !important;
    }
  }
}

// 返回顶部按钮
.back-to-top {
  position: fixed;
  bottom: $space-2xl;
  right: $space-2xl;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, $primary-color 0%, $primary-hover 100%);
  color: white;
  border: none;
  border-radius: 50%;
  box-shadow: $shadow-3;
  cursor: pointer;
  opacity: 0.9;
  transition: all $transition-base;
  @include flex-center;
  font-size: 20px;
  font-weight: bold;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  z-index: $z-index-fixed;
  
  &:hover {
    opacity: 1;
    transform: translateY(-4px) scale(1.05);
    box-shadow: $shadow-hover;
  }
  
  &:active {
    transform: translateY(-2px) scale(1.02);
  }
  
  @include mobile {
    width: 48px;
    height: 48px;
    bottom: $space-lg;
    right: $space-lg;
  }
}

// 搜索框
.search-box {
  background: $bg-primary;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  padding: $space-2xl 0;
  position: sticky;
  top: 0;
  z-index: $z-index-sticky;
  box-shadow: $shadow-2;
  border-bottom: 1px solid $border-light;
  transition: all $transition-base;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
    z-index: -1;
  }
  
  &:has(.input:focus) {
    transform: translateY(-2px);
    box-shadow: $shadow-3;
  }
  
  @include mobile {
    padding: $space-lg 0;
  }
}

// 导航栏
.navbar {
  background: white;
  border-bottom: 1px solid $border-light;
  padding: $space-lg 0;
  position: sticky;
  top: 0;
  z-index: $z-index-sticky;
  box-shadow: $shadow-1;
  
  &__content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 $space-lg;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    @media (max-width: #{$breakpoint-mobile - 1px}) {
      padding: 0 $space-md;
    }
  }
  
  &__brand {
    @include flex-start;
    gap: $space-md;
    
    h1 {
      font-size: $font-size-xl;
      margin: 0;
    }
  }
  
  &__actions {
    @include flex-start;
    gap: $space-md;
  }
}

// 用户信息
.user-info {
  @include flex-start;
  gap: $space-sm;
  
  &__avatar {
    flex-shrink: 0;
  }
  
  &__details {
    min-width: 0;
  }
  
  &__name {
    font-weight: 600;
    color: $text-primary;
    @include text-ellipsis;
  }
  
  &__meta {
    font-size: $font-size-xs;
    color: $text-tertiary;
    @include text-ellipsis;
  }
}

// 图片网格
.image-grid {
  display: grid;
  gap: $space-sm;
  
  &--single {
    grid-template-columns: 1fr;
    max-width: 400px;
  }
  
  &--multiple {
    grid-template-columns: repeat(3, 1fr);
  }
  
  &__item {
    width: 100%;
    aspect-ratio: 1 / 1;
    border-radius: $radius-md;
    background: $bg-tertiary;
    object-fit: cover;
    cursor: pointer;
    transition: all $transition-base;
    overflow: hidden;
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0);
      transition: background $transition-base;
      z-index: 1;
      pointer-events: none;
    }
    
    &:hover {
      transform: scale(1.02);
      box-shadow: $shadow-3;
      
      &::before {
        background: rgba(0, 0, 0, 0.1);
      }
      
      img {
        transform: scale(1.05);
      }
    }
    
    img {
      transition: transform $transition-slow;
    }
  }
  
  &__placeholder {
    @include flex-center;
    width: 100%;
    height: 100%;
    background: $bg-tertiary;
    color: $text-tertiary;
    font-size: 24px;
  }
}

// 平板设备适配
@include tablet {
  .container {
    padding: 0 $space-xl;
  }
  
  .card {
    &__header,
    &__body {
      padding: $space-xl;
    }
  }
  
  .user-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: $space-xl;
  }
  
  .image-grid {
    &.multi-images {
      grid-template-columns: repeat(3, 1fr);
    }
  }
  
  .el-button {
    min-height: 36px;
    padding: $space-sm $space-lg;
  }
  
  .el-input {
    .el-input__wrapper {
      min-height: 36px;
      padding: $space-sm $space-md;
    }
  }
}

// 响应式调整
@include mobile {
  .card {
    margin: 0 0 $space-md 0;
    border-radius: $radius-lg;
    
    &__header,
    &__body {
      padding: $space-lg;
    }
    
    &__footer {
      padding: $space-md $space-lg;
    }
  }
  
  .avatar {
    &--medium {
      width: 36px;
      height: 36px;
    }
  }
  
  .user-info {
    &__name {
      font-size: $font-size-sm;
    }
    
    &__meta {
      font-size: 11px;
    }
  }
  
  // 移动端触摸优化
  .el-button {
    min-height: 44px; // iOS推荐的最小触摸区域
    padding: $space-sm $space-lg;
    
    &.el-button--small {
      min-height: 36px;
      padding: $space-xs $space-md;
    }
  }
  
  // 移动端输入框优化
  .el-input {
    .el-input__wrapper {
      min-height: 44px;
      padding: $space-md;
      font-size: 16px; // 防止iOS缩放
    }
    
    &.el-input--small {
      .el-input__wrapper {
        min-height: 36px;
        padding: $space-sm;
        font-size: 14px;
      }
    }
  }
}

// 暗色模式适配
@include dark-mode {
  .card {
    background: $dark-bg-primary;
    border-color: $dark-border-light;
    
    &__header {
      background: linear-gradient(135deg, rgba(36, 37, 38, 0.8) 0%, rgba(58, 59, 60, 0.6) 100%);
      border-color: $dark-border-light;
    }
    
    &__footer {
      background: linear-gradient(135deg, rgba(36, 37, 38, 0.5) 0%, rgba(58, 59, 60, 0.3) 100%);
      border-color: $dark-border-light;
    }
  }
  
  .search-box {
    background: $dark-bg-primary;
    border-color: $dark-border-light;
    
    &::before {
      background: linear-gradient(135deg, rgba(36, 37, 38, 0.9) 0%, rgba(58, 59, 60, 0.8) 100%);
    }
  }
  
  .navbar {
    background: $dark-bg-primary;
    border-color: $dark-border-light;
  }
  
  .user-info {
    &__name {
      color: $dark-text-primary;
    }
    
    &__meta {
      color: $dark-text-tertiary;
    }
  }
}