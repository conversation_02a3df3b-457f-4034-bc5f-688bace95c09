import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')
  
  return {
    plugins: [vue()],
    resolve: {
      alias: {
        '@': '/src'
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler', // 使用现代 Sass API
          silenceDeprecations: ['legacy-js-api'], // 暂时静默弃用警告
          additionalData: `@use "@/styles/variables.scss" as *; @use "@/styles/mixins.scss" as *;`
        }
      }
    },
    server: {
      port: 3000,
      open: true,
      // 开发服务器代理配置 - 解决 CORS 问题
      proxy: {
        '/api': {
          target: env.VITE_API_BASE_URL || 'https://weibo.api.maikebuke.com',
          changeOrigin: true,
          secure: true,
          rewrite: (path) => path.replace(/^\/api/, '/api')
        }
      }
    },
    build: {
      outDir: 'dist',
      // 根据环境设置不同的构建配置
      minify: mode === 'production' ? 'esbuild' : false,
      sourcemap: mode !== 'production',
      // 为 Vercel 部署优化
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['vue', 'vue-router'],
            elementPlus: ['element-plus', '@element-plus/icons-vue']
          }
        }
      }
    },
    // 定义全局常量
    define: {
      __APP_ENV__: JSON.stringify(mode),
      __API_BASE_URL__: JSON.stringify(env.VITE_API_BASE_URL)
    }
  }
})