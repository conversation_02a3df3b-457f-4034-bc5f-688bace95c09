<!DOCTYPE html>
<html>
<head>
    <title>OAuth2 状态显示</title>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="https://unpkg.com/element-ui@2.15.14/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2.7.16/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui@2.15.14/lib/index.js"></script>
    <script src="https://unpkg.com/axios@1.6.8/dist/axios.min.js"></script>
    <script src="https://unpkg.com/dompurify@3.2.3/dist/purify.min.js"></script>
    <script src="https://unpkg.com/luxon@3.5.0/build/global/luxon.min.js"></script>
    <style>
        body {
            font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f2f2f2;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        #app {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 30px;
            width: 80%;
            max-width: 800px;
            text-align: center;
            position: relative; /* 使 #app 成为定位上下文 */
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
        }

        p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        strong {
            font-weight: bold;
            color: #333;
        }

        a {
            color: #409EFF;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        .el-button {
            margin-top: 20px;
        }

        .status-container {
            margin-top: 20px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            text-align: left;
        }

        .status-item {
            margin-bottom: 10px;
        }

        /* “正在跳转”消息的样式 */
        .redirecting-overlay {
            position: fixed; /* 悬浮在屏幕上 */
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5); /* 半透明背景 */
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000; /* 确保在最上层 */
        }

        .redirecting-message {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            font-size: 1.2em;
            color: green;
            font-style: italic;
            display: flex;           /* 使用 Flexbox */
            flex-direction: column; /* 垂直排列 */
            align-items: center;      /* 水平居中子元素 */
        }

        /* 加载动画 */
        .loading-spinner {
            border: 5px solid #f3f3f3; /* Light grey */
            border-top: 5px solid #3498db; /* Blue */
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 2s linear infinite;
            margin-bottom: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="app">
        <h1>OAuth2 状态显示</h1>

        <el-card class="box-card" v-if="isDebugMode">
            <div slot="header" class="clearfix">
                <span>OAuth 链接</span>
            </div>
            <div>
                <p>
                    <a :href="oauthUrlComputed" target="_blank">{{ oauthUrlComputed }}</a>
                </p>
            </div>
        </el-card>

        <el-button type="primary" @click="triggerOAuth" :disabled="clicked">点击进行 OAuth 授权</el-button>

        <div class="status-container" v-if="isDebugMode">
            <h2>状态信息</h2>
            <div class="status-item" v-if="ret_code"><strong>Code:</strong> {{ ret_code }}</div>
            <div class="status-item" v-if="ret_state"><strong>State:</strong> {{ ret_state }}</div>
        </div>

        <!-- "正在跳转" 提示 -->
        <div class="redirecting-overlay" v-if="redirecting">
            <div class="redirecting-message">
                <div class="loading-spinner"></div>
                正在跳转中, 请稍后...
            </div>
        </div>
    </div>
    <script>
        new Vue({
            el: '#app',
            data: {
                // 从 Server 传递的 OAuth 参数
                appid: "{{ oauth_params.appid }}",
                agentid: "{{ oauth_params.agentid }}",
                scope: "{{ oauth_params.scope }}",
                state: "{{ oauth_params.state }}",
                redirect_uri: "{{ oauth_params.redirect_uri }}",
                clicked: false,

                // 从 URL 解析的返回参数
                ret_code: null,
                ret_state: null,
                ret_redirect: null,
                redirecting: false, // 控制“正在跳转”消息的显示
                isDebugMode: false    // 控制 OAuth 链接的显示
            },
            computed: {
                // 计算属性，动态生成 OAuth 授权链接
                oauthUrlComputed() {
                    const encodedRedirectUri = encodeURIComponent(this.redirect_uri);
                    return `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${this.appid}&redirect_uri=${encodedRedirectUri}&response_type=code&scope=${this.scope}&state=${this.state}&agentid=${this.agentid}#wechat_redirect`;
                }
            },
            mounted() {
                this.parseUrlParams(); // 在 Vue 实例挂载后，解析 URL 参数
                this.checkDebugMode(); // 检查是否开启 Debug 模式
                if (!this.ret_code) {
                    this.triggerOAuth();
                }
            },
            methods: {
                // 触发 OAuth 授权流程
                triggerOAuth() {
                    this.clicked = true;
                    window.location.href = this.oauthUrlComputed;
                },

                // 解析 URL 参数
                parseUrlParams() {
                    try {
                        const urlParams = new URLSearchParams(window.location.search);
                        this.ret_code = urlParams.get('code');
                        this.ret_state = urlParams.get('state');
                        this.ret_redirect = urlParams.get('backto');

                        this.handleRedirect(); // 处理重定向
                    } catch (error) {
                        console.error("解析 URL 参数出错:", error);
                        this.$message.error("解析 URL 参数失败，请检查 URL。"); // 使用 Element UI 的 Message 组件显示错误信息
                    }
                },

                // 处理重定向
                handleRedirect() {
                    if (this.ret_redirect) {
                        this.redirecting = true; // 设置 redirecting 为 true，显示“正在跳转”消息

                        Vue.nextTick(() => {
                            setTimeout(() => {
                                window.location.href = this.ret_redirect;
                            }, 500);
                        });
                    }
                },

                // 检查是否开启 Debug 模式
                checkDebugMode() {
                    const urlParams = new URLSearchParams(window.location.search);
                    this.isDebugMode = urlParams.has('debug'); // 如果 URL 中包含 debug 参数，则开启 Debug 模式
                }
            }
        });
    </script>
</body>
</html>