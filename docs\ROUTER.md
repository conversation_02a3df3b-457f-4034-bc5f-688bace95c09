# 路由结构文档

## 概述

应用已重构为使用 Vue Router 4 的 Hash 路由模式，支持多页面导航。

## 路由配置

### 主要路由

1. **首页** - `/` 或 `/#/`
   - 组件: `src/views/Home.vue`
   - 功能: 显示示例用户列表，提供导航入口

2. **用户资料页** - `/#/user/:userId`
   - 组件: `src/views/UserProfile.vue`
   - 功能: 显示指定用户的微博内容
   - 参数: `userId` - 用户ID（必须为数字）
   - 特性: 卡片可点击跳转到详情页

3. **微博详情页** - `/#/detail/:weiboId`
   - 组件: `src/views/WeiboDetail.vue`
   - 功能: 显示单条微博的详细内容
   - 参数: `weiboId` - 微博ID
   - 数据源: `/data/weibo/ajax/{weiboId}.json`

4. **404页面** - `/#/404`
   - 组件: `src/views/NotFound.vue`
   - 功能: 处理无效路由

## URL 示例

- 首页: `http://localhost:3000/#/`
- 用户A: `http://localhost:3000/#/user/5314536271`
- 用户B: `http://localhost:3000/#/user/6475346402`
- 用户C: `http://localhost:3000/#/user/7777211754`
- 微博详情: `http://localhost:3000/#/detail/{weiboId}`

## 路由守卫

- 用户ID验证: 确保用户ID参数为纯数字格式
- 无效路由重定向: 自动重定向到404页面

## 组件结构

```
src/
├── App.vue              # 主应用容器
├── router/
│   └── index.js         # 路由配置
├── views/               # 页面组件
│   ├── Home.vue         # 首页
│   ├── UserProfile.vue  # 用户资料页
│   ├── WeiboDetail.vue  # 微博详情页
│   └── NotFound.vue     # 404页面
└── components/          # 复用组件
    └── WeiboCard.vue    # 微博卡片组件
```

## 导航方式

1. **程序化导航**
   ```javascript
   import { useRouter } from 'vue-router'
   
   const router = useRouter()
   router.push('/user/5314536271')
   ```

2. **声明式导航**
   ```vue
   <router-link to="/user/5314536271">用户A</router-link>
   ```

## 扩展指南

添加新页面的步骤：

1. 在 `src/views/` 创建新的 Vue 组件
2. 在 `src/router/index.js` 添加路由配置
3. 根据需要添加路由守卫或参数验证
4. 更新导航菜单或链接

## 最佳实践

- 使用 Hash 路由避免服务器配置问题
- 路由参数验证确保数据安全
- 组件懒加载优化性能（可选）
- 保持路由结构清晰简洁