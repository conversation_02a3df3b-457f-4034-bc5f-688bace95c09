<template>
  <div class="auth-test" v-if="showTest">
    <el-card class="test-card">
      <template #header>
        <div class="test-header">
          <span>认证状态测试</span>
          <el-button @click="showTest = false" type="text" size="small">关闭</el-button>
        </div>
      </template>
      
      <div class="test-content">
        <div class="status-section">
          <h4>当前状态</h4>
          <el-descriptions :column="1" size="small">
            <el-descriptions-item label="认证状态">
              <el-tag :type="isAuthenticated ? 'success' : 'danger'">
                {{ isAuthenticated ? '已认证' : '未认证' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="Token">
              <code>{{ authToken ? authToken.substring(0, 20) + '...' : '无' }}</code>
            </el-descriptions-item>
            <el-descriptions-item label="用户信息">
              <code>{{ userInfo ? JSON.stringify(userInfo, null, 2) : '无' }}</code>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        
        <div class="actions-section">
          <h4>测试操作</h4>
          <el-space wrap>
            <el-button @click="refreshAuth" type="primary" size="small">
              刷新状态
            </el-button>
            <el-button @click="testApiCall" type="success" size="small" :loading="testing">
              测试 API 调用
            </el-button>
            <el-button @click="clearAuth" type="danger" size="small">
              清空认证
            </el-button>
            <el-button @click="startAuth" type="warning" size="small">
              开始认证
            </el-button>
          </el-space>
        </div>
        
        <div class="logs-section" v-if="logs.length > 0">
          <h4>测试日志</h4>
          <div class="logs">
            <div v-for="(log, index) in logs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span :class="['log-level', log.level]">{{ log.level }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useAuth } from '../stores/auth.js'
import { getApiUrl } from '../config/api.js'
import axios from 'axios'

const showTest = ref(import.meta.env.DEV)
const testing = ref(false)
const logs = ref([])

const { 
  isAuthenticated, 
  authToken, 
  userInfo, 
  refreshAuth: authRefresh, 
  logout, 
  startAuth: authStart,
  getAuthHeaders 
} = useAuth()

const addLog = (level, message) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    level,
    message
  })
  
  // 只保留最近 10 条日志
  if (logs.value.length > 10) {
    logs.value = logs.value.slice(0, 10)
  }
}

const refreshAuth = () => {
  addLog('INFO', '刷新认证状态...')
  authRefresh()
  addLog('SUCCESS', `认证状态: ${isAuthenticated.value ? '已认证' : '未认证'}`)
}

const testApiCall = async () => {
  testing.value = true
  addLog('INFO', '测试 API 调用...')
  
  try {
    const headers = getAuthHeaders()
    addLog('INFO', `使用认证头部: ${JSON.stringify(headers)}`)
    
    const response = await axios.get(getApiUrl('/api/get_subscribe'), { headers })
    addLog('SUCCESS', `API 调用成功: ${response.status}`)
    addLog('INFO', `返回数据: ${JSON.stringify(response.data).substring(0, 100)}...`)
  } catch (error) {
    addLog('ERROR', `API 调用失败: ${error.message}`)
    if (error.response) {
      addLog('ERROR', `响应状态: ${error.response.status}`)
    }
  } finally {
    testing.value = false
  }
}

const clearAuth = () => {
  addLog('INFO', '清空认证信息...')
  logout()
  addLog('SUCCESS', '认证信息已清空')
}

const startAuth = () => {
  addLog('INFO', '开始认证流程...')
  authStart()
}
</script>

<style scoped>
.auth-test {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  width: 400px;
  max-height: 80vh;
  overflow-y: auto;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-section,
.actions-section,
.logs-section {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 12px;
}

.logs {
  max-height: 200px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 12px;
}

.log-item {
  display: flex;
  gap: 8px;
  margin-bottom: 4px;
  align-items: center;
}

.log-time {
  color: #909399;
  min-width: 80px;
}

.log-level {
  min-width: 60px;
  font-weight: bold;
}

.log-level.INFO {
  color: #409eff;
}

.log-level.SUCCESS {
  color: #67c23a;
}

.log-level.ERROR {
  color: #f56c6c;
}

.log-message {
  flex: 1;
  word-break: break-all;
}

code {
  background: #f5f7fa;
  padding: 2px 4px;
  border-radius: 2px;
  font-size: 12px;
}
</style>