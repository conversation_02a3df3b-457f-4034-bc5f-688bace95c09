# 环境配置示例文件
# 复制此文件为 .env.local 并填入真实的配置值

# API 配置
VITE_API_BASE_URL=http://localhost:8080
VITE_APP_ENV=development
VITE_APP_TITLE=微博查看器

# OAuth 配置
# 请替换为真实的微信企业应用配置
VITE_OAUTH_APPID=your_wechat_appid_here
VITE_OAUTH_AGENTID=your_wechat_agentid_here
VITE_OAUTH_SCOPE=snsapi_base
VITE_OAUTH_STATE=your_random_state_string_here

# 注意事项：
# 1. VITE_OAUTH_APPID: 微信企业应用的 AppID
# 2. VITE_OAUTH_AGENTID: 微信企业应用的 AgentID  
# 3. VITE_OAUTH_STATE: 随机字符串，用于防止 CSRF 攻击
# 4. 所有以 VITE_ 开头的变量都会暴露给前端代码