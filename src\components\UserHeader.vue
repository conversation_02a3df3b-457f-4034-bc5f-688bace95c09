<template>
  <div class="card-header">
    <div>
      <el-avatar :src="avatarUrl"></el-avatar>
      <div class="card-title">
        <div class="user-name">{{ user.screen_name }}</div>
      </div>
    </div>
    <div class="post-info">
      <span class="post-date">{{ formattedDate }}</span>
      <span v-html="formatText(source)"></span>
      <span v-if="regionName">{{ regionName }}</span>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { imageProxy } from '../utils/imageProxy'
import { textFormatter } from '../utils/textFormatter'
import { DateTime } from 'luxon'

export default {
  name: 'UserHeader',
  props: {
    user: {
      type: Object,
      required: true
    },
    createdAt: {
      type: String,
      required: true
    },
    source: {
      type: String,
      default: ''
    },
    regionName: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const avatarUrl = computed(() => {
      return imageProxy.proxyImage(props.user.profile_image_url)
    })

    const formattedDate = computed(() => {
      const cardTime = new Date(props.createdAt)
      const dateTime = DateTime.fromMillis(cardTime.getTime())
      return dateTime.setLocale('zh').toFormat('yyyy年MM月dd日 HH:mm:ss')
    })

    const formatText = (text) => {
      return textFormatter.formatText(text)
    }

    return {
      avatarUrl,
      formattedDate,
      formatText
    }
  }
}
</script>

<style lang="scss" scoped>
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  
  > div:first-child {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
  }
}

// 头像样式优化
:deep(.el-avatar) {
  width: 40px !important;
  height: 40px !important;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid $border-light;
  transition: all $transition-base;
  flex-shrink: 0;
  
  &:hover {
    border-color: $primary-color;
    transform: scale(1.05);
  }
  
  @include mobile {
    width: 36px !important;
    height: 36px !important;
  }
}

// 用户信息区域
.card-title {
  margin-left: $space-md;
  flex: 1;
  min-width: 0;
  
  @include mobile {
    margin-left: $space-sm;
  }
}

.user-name {
  font-weight: 600;
  color: $text-primary;
  font-size: $font-size-base;
  line-height: $line-height-tight;
  margin: 0;
  @include text-ellipsis;
  
  @include mobile {
    font-size: $font-size-sm;
  }
}

// 发布信息样式
.post-info {
  color: $text-tertiary;
  font-size: $font-size-xs;
  text-align: right;
  display: flex;
  flex-direction: column;
  gap: $space-xs;
  align-items: flex-end;
  flex-shrink: 0;
  
  @include mobile {
    font-size: 11px;
    gap: 2px;
  }
  
  span {
    white-space: nowrap;
    padding: 2px 0;
    transition: color $transition-fast;
    line-height: 1.2;
    
    &:hover {
      color: $text-secondary;
    }
  }
  
  .post-date {
    font-weight: 500;
    color: $text-secondary !important;
    font-size: $font-size-xs;
    
    @include mobile {
      font-size: 11px;
    }
  }
}

// 来源和地区信息样式
:deep(.post-info) {
  // 处理来源信息中的链接
  a {
    color: inherit;
    text-decoration: none;
    transition: color $transition-fast;
    
    &:hover {
      color: $primary-color;
    }
  }
  
  // 标签样式
  .topic-tag,
  .at-user-tag {
    display: inline-block;
    padding: 1px 4px;
    border-radius: $radius-sm;
    font-size: 10px;
    font-weight: 500;
    margin: 0 1px;
    transition: all $transition-fast;
    
    @include mobile {
      font-size: 9px;
      padding: 1px 3px;
    }
  }
  
  .topic-tag {
    color: $primary-color;
    background: $primary-light;
    
    &:hover {
      background: $primary-color;
      color: white;
      transform: translateY(-1px);
    }
  }
  
  .at-user-tag {
    color: $primary-color;
    background: $primary-light;
    
    &:hover {
      background: $primary-color;
      color: white;
      transform: translateY(-1px);
    }
  }
}

// 响应式优化
@include mobile {
  .card-header {
    align-items: flex-start;
    
    > div:first-child {
      align-items: flex-start;
    }
  }
  
  .post-info {
    margin-top: 2px;
  }
}

// 暗色模式适配
@include dark-mode {
  .user-name {
    color: $dark-text-primary;
  }
  
  .post-info {
    color: $dark-text-tertiary;
    
    span:hover {
      color: $dark-text-secondary;
    }
    
    .post-date {
      color: $dark-text-secondary !important;
    }
  }
  
  :deep(.el-avatar) {
    border-color: $dark-border-light;
    
    &:hover {
      border-color: $primary-color;
    }
  }
}
</style>