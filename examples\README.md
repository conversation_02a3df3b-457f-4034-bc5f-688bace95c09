# 示例代码

这个目录包含了项目的原始版本和示例代码。

## 文件说明

### single_page.html
这是项目的原始版本，是一个完整的单页面 HTML 文件，包含了所有的功能：

- **技术栈**: Vue 2 + Element UI + 原生 JavaScript
- **特点**: 
  - 单文件包含所有代码（HTML、CSS、JavaScript）
  - 直接在浏览器中运行，无需构建过程
  - 使用 CDN 引入依赖
  - 完整的微博展示功能

### 功能特性
- 微博卡片展示
- 无限滚动加载
- 搜索功能
- 图片预览
- 转发内容显示
- 响应式设计
- 返回顶部按钮

### 使用方法
直接在浏览器中打开 `single_page.html` 即可运行。

## 项目演进

```
single_page.html (原始版本)
    ↓
现代化 Vue 3 项目 (当前版本)
```

原始的单页面版本被重构为现代化的 Vue 3 + Vite 项目，具有更好的：
- 代码组织结构
- 开发体验
- 构建优化
- 类型安全
- 可维护性