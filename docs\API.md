# API 文档

本文档描述了微博应用所使用的 API 接口规范。

## 基础信息

- **Base URL**: `/api`
- **数据格式**: JSON
- **字符编码**: UTF-8

## 接口列表

### 获取微博列表

获取指定用户的微博列表，支持分页和搜索。

#### 请求信息

- **URL**: `/api/tweets`
- **方法**: `GET`
- **参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user | string | 是 | 用户ID |
| limit | number | 否 | 每页数量，默认5 |
| cursor | string | 否 | 分页游标，用于获取下一页 |
| keyword | string | 否 | 搜索关键词 |

#### 请求示例

```javascript
GET /api/tweets?user=123456&limit=5&cursor=abc123&keyword=搜索词
```

#### 响应格式

```javascript
{
  "success": true,
  "data": [
    {
      "id": "4751234567890123456",
      "created_at": "2024-01-15T10:30:00.000Z",
      "text": "微博正文内容 #话题# @用户名",
      "source": "<a href=\"http://weibo.com/\" rel=\"nofollow\">微博网页版</a>",
      "region_name": "北京",
      "user": {
        "id": "123456",
        "screen_name": "用户昵称",
        "profile_image_url": "https://example.com/avatar.jpg"
      },
      "pic_infos": {
        "pic_id_1": {
          "large": {
            "url": "https://example.com/large_image.jpg"
          },
          "original": {
            "url": "https://example.com/original_image.jpg"
          }
        }
      },
      "pics": [
        {
          "large": {
            "url": "https://example.com/large_image.jpg"
          },
          "original": {
            "url": "https://example.com/original_image.jpg"
          },
          "url": "https://example.com/image.jpg"
        }
      ],
      "retweeted_status": {
        "id": "4751234567890123457",
        "text": "转发的微博内容",
        "user": {
          "screen_name": "原博主昵称"
        },
        "pic_infos": {},
        "pics": []
      }
    }
  ],
  "next_cursor": "def456"
}
```

#### 响应字段说明

##### 根级字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| data | array | 微博数据数组 |
| next_cursor | string | 下一页游标，为空表示没有更多数据 |

##### 微博对象字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | string | 微博ID |
| created_at | string | 发布时间 (ISO 8601格式) |
| text | string | 微博正文内容 (可能包含HTML标签) |
| source | string | 发布来源 (HTML格式) |
| region_name | string | 发布地区 |
| user | object | 用户信息对象 |
| pic_infos | object | 图片信息对象 (键值对格式) |
| pics | array | 图片数组 |
| retweeted_status | object | 转发的微博对象 (可选) |

##### 用户对象字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | string | 用户ID |
| screen_name | string | 用户昵称 |
| profile_image_url | string | 头像URL |

##### 图片对象字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| large | object | 大图信息 |
| original | object | 原图信息 |
| url | string | 图片URL |

#### 错误响应

```javascript
{
  "success": false,
  "error": "错误信息",
  "code": "ERROR_CODE"
}
```

#### 状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 404 | 用户不存在 |
| 429 | 请求频率过高 |
| 500 | 服务器内部错误 |

## 数据处理

### 文本内容处理

微博正文可能包含以下特殊内容：

1. **话题标签**: `#话题名#`
2. **@用户**: `@用户名`
3. **链接**: `<a href="...">链接文本</a>`
4. **表情**: `<img src="..." alt="表情" />`

### 图片处理

- 图片URL可能需要通过代理服务访问
- 支持多种尺寸：`large`、`original`
- 建议使用懒加载优化性能

### 时间格式

- 服务器返回 ISO 8601 格式时间
- 前端需要转换为本地时间显示
- 建议格式：`yyyy年MM月dd日 HH:mm:ss`

## 使用示例

### JavaScript 调用示例

```javascript
import axios from 'axios'

class WeiboService {
  async getTweets(params) {
    try {
      const { data } = await axios.get('/api/tweets', { params })
      return data
    } catch (error) {
      console.error('API request failed:', error)
      throw error
    }
  }
}

// 使用示例
const service = new WeiboService()
const result = await service.getTweets({
  user: '123456',
  limit: 10,
  keyword: '搜索关键词'
})
```

### 分页处理示例

```javascript
let cursor = null
const allTweets = []

while (true) {
  const result = await service.getTweets({
    user: '123456',
    limit: 20,
    cursor: cursor
  })
  
  if (!result.success || !result.data.length) {
    break
  }
  
  allTweets.push(...result.data)
  cursor = result.next_cursor
  
  if (!cursor) {
    break // 没有更多数据
  }
}
```

## 注意事项

1. **频率限制**: 建议控制请求频率，避免被限流
2. **错误处理**: 务必处理各种错误情况
3. **数据缓存**: 可以实现本地缓存减少请求
4. **图片代理**: 图片URL可能需要代理访问
5. **内容安全**: 对用户生成内容进行适当的安全处理