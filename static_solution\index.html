<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微博查看器 - 静态版本</title>
    <link rel="icon" href="https://m.weibo.cn/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="https://unpkg.com/element-ui@2.15.14/lib/theme-chalk/index.css">
    <style>
        /* 复用 single_homepage.html 的样式 */
        :root {
            --primary-color: #0084ff;
            --secondary-color: #606266;
            --text-color: #303133;
            --border-color: #e4e7ed;
            --background-color: #f2f3f5;
            --card-background: #fff;
            --shadow-color: rgba(0, 0, 0, 0.05);
            --hover-shadow-color: rgba(0, 0, 0, 0.12);
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 15px;
        }

        /* 认证状态栏 */
        .auth-bar {
            background: var(--card-background);
            padding: 15px 0;
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 1001;
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .auth-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .auth-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-authenticated {
            background: #f0f9ff;
            color: #0369a1;
            border: 1px solid #bae6fd;
        }

        .status-unauthenticated {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        /* 搜索框样式 */
        .search-box {
            background: var(--card-background);
            padding: 20px 0;
            position: sticky;
            top: 60px;
            z-index: 1000;
            box-shadow: 0 2px 8px var(--shadow-color);
        }

        /* 未认证提示 */
        .auth-prompt {
            text-align: center;
            padding: 60px 20px;
            background: var(--card-background);
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 2px 8px var(--shadow-color);
        }

        .auth-prompt h2 {
            color: var(--text-color);
            margin-bottom: 16px;
        }

        .auth-prompt p {
            color: var(--secondary-color);
            margin-bottom: 24px;
            line-height: 1.6;
        }

        /* 复用其他样式... */
        .box-card {
            width: 100%;
            margin: 20px 0;
            border-radius: 12px;
            border: none;
            transition: transform 0.2s, box-shadow 0.2s;
            background: var(--card-background);
            box-shadow: 0 2px 8px var(--shadow-color);
        }

        .box-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px var(--hover-shadow-color);
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .card-content {
            padding: 16px 20px;
            line-height: 1.6;
            color: var(--text-color);
        }

        .loading-container {
            text-align: center;
            padding: 40px;
            color: var(--secondary-color);
        }
    </style>
</head>

<body>
    <div id="app">
        <!-- 认证状态栏 -->
        <div class="auth-bar">
            <div class="container">
                <div class="auth-status">
                    <div class="auth-info">
                        <span :class="['status-indicator', isAuthenticated ? 'status-authenticated' : 'status-unauthenticated']">
                            {{ isAuthenticated ? '✓ 已认证' : '✗ 未认证' }}
                        </span>
                        <span v-if="userInfo" class="user-name">{{ userInfo.name || '用户' }}</span>
                    </div>
                    <div class="auth-actions">
                        <el-button v-if="!isAuthenticated" type="primary" size="small" @click="startAuth">
                            立即认证
                        </el-button>
                        <el-button v-else type="text" size="small" @click="logout">
                            退出登录
                        </el-button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索框 -->
        <div v-if="isAuthenticated" class="search-box">
            <div class="container">
                <el-input 
                    v-model="keyword" 
                    placeholder="搜索微博内容..." 
                    clearable 
                    @keyup.enter.native="handleSearch"
                >
                    <i slot="prefix" class="el-input__icon el-icon-search"></i>
                </el-input>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="container">
            <!-- 未认证提示 -->
            <div v-if="!isAuthenticated" class="auth-prompt">
                <h2>需要身份认证</h2>
                <p>为了访问微博内容，请先完成身份认证。认证过程安全可靠，我们不会存储您的敏感信息。</p>
                <el-button type="primary" size="large" @click="startAuth">
                    开始认证
                </el-button>
            </div>

            <!-- 已认证内容 -->
            <template v-else>
                <!-- 用户选择 -->
                <div v-if="!selectedUserId" class="user-selection">
                    <h3>选择要查看的用户</h3>
                    <div class="user-cards">
                        <div 
                            v-for="user in sampleUsers" 
                            :key="user.id"
                            class="user-card"
                            @click="selectUser(user.id)"
                        >
                            <div class="user-avatar">{{ user.name.charAt(0) }}</div>
                            <div class="user-info">
                                <h4>{{ user.name }}</h4>
                                <p>ID: {{ user.id }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 微博内容 -->
                <div v-else>
                    <div class="user-header">
                        <el-button type="text" @click="selectedUserId = null">
                            ← 返回用户选择
                        </el-button>
                        <span>用户ID: {{ selectedUserId }}</span>
                    </div>

                    <!-- 加载状态 -->
                    <div v-if="loading" class="loading-container">
                        <el-skeleton :rows="3" animated />
                    </div>

                    <!-- 微博列表 -->
                    <div v-else>
                        <div v-for="tweet in tweets" :key="tweet.id" class="box-card">
                            <div class="card-header">
                                <div>
                                    <strong>{{ tweet.user?.screen_name || '用户' }}</strong>
                                </div>
                                <div>
                                    <small>{{ formatDate(tweet.created_at) }}</small>
                                </div>
                            </div>
                            <div class="card-content">
                                {{ tweet.text || '内容加载中...' }}
                            </div>
                        </div>

                        <div v-if="!tweets.length && !loading" class="loading-container">
                            暂无数据
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>

    <!-- 依赖引入 -->
    <script src="https://unpkg.com/vue@2.7.16/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui@2.15.14/lib/index.js"></script>
    <script src="https://unpkg.com/axios@1.6.8/dist/axios.min.js"></script>
    
    <script>
        new Vue({
            el: '#app',
            data: {
                // 认证状态
                isAuthenticated: false,
                userInfo: null,
                authToken: null,

                // 用户选择
                selectedUserId: null,
                sampleUsers: [
                    { id: '5314536271', name: '用户A' },
                    { id: '6475346402', name: '用户B' },
                    { id: '7777211754', name: '用户C' }
                ],

                // 微博数据
                tweets: [],
                loading: false,
                keyword: ''
            },

            mounted() {
                this.checkAuthStatus();
                this.handleAuthCallback();
            },

            methods: {
                // 检查认证状态
                checkAuthStatus() {
                    // 从 URL 参数或 localStorage 检查认证状态
                    const urlParams = new URLSearchParams(window.location.search);
                    const token = urlParams.get('token') || localStorage.getItem('auth_token');
                    const userInfo = urlParams.get('user') || localStorage.getItem('user_info');

                    if (token) {
                        this.isAuthenticated = true;
                        this.authToken = token;
                        localStorage.setItem('auth_token', token);
                        
                        if (userInfo) {
                            try {
                                this.userInfo = JSON.parse(decodeURIComponent(userInfo));
                                localStorage.setItem('user_info', userInfo);
                            } catch (e) {
                                console.warn('Failed to parse user info:', e);
                            }
                        }

                        // 清理 URL 参数
                        if (urlParams.get('token')) {
                            const cleanUrl = window.location.pathname;
                            window.history.replaceState({}, document.title, cleanUrl);
                        }
                    }
                },

                // 处理认证回调
                handleAuthCallback() {
                    const urlParams = new URLSearchParams(window.location.search);
                    if (urlParams.get('auth_success') === 'true') {
                        this.$message.success('认证成功！');
                    } else if (urlParams.get('auth_error')) {
                        this.$message.error('认证失败：' + urlParams.get('auth_error'));
                    }
                },

                // 开始认证流程
                startAuth() {
                    // 跳转到后端的 OAuth 处理页面
                    const backUrl = encodeURIComponent(window.location.href);
                    const authUrl = `/api/oauth/start?backto=${backUrl}`;
                    window.location.href = authUrl;
                },

                // 登出
                logout() {
                    this.isAuthenticated = false;
                    this.authToken = null;
                    this.userInfo = null;
                    this.selectedUserId = null;
                    this.tweets = [];
                    
                    localStorage.removeItem('auth_token');
                    localStorage.removeItem('user_info');
                    
                    this.$message.success('已退出登录');
                },

                // 选择用户
                selectUser(userId) {
                    this.selectedUserId = userId;
                    this.loadTweets();
                },

                // 加载微博数据
                async loadTweets() {
                    if (!this.authToken || !this.selectedUserId) return;

                    try {
                        this.loading = true;
                        const response = await axios.get('/api/tweets', {
                            params: {
                                user: this.selectedUserId,
                                limit: 10,
                                keyword: this.keyword
                            },
                            headers: {
                                'Authorization': `Bearer ${this.authToken}`
                            }
                        });

                        if (response.data?.success) {
                            this.tweets = response.data.data || [];
                        } else {
                            this.$message.error('加载失败：' + (response.data?.message || '未知错误'));
                        }
                    } catch (error) {
                        console.error('Load tweets error:', error);
                        this.$message.error('网络错误，请稍后重试');
                    } finally {
                        this.loading = false;
                    }
                },

                // 搜索处理
                handleSearch() {
                    if (this.selectedUserId) {
                        this.loadTweets();
                    }
                },

                // 格式化日期
                formatDate(dateStr) {
                    if (!dateStr) return '';
                    const date = new Date(dateStr);
                    return date.toLocaleString('zh-CN');
                }
            }
        });
    </script>
</body>
</html>