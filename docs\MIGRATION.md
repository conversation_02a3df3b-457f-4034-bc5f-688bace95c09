# 迁移指南

本文档描述了从单页面 HTML 版本迁移到 Vue 3 + Vite 版本的详细过程。

## 迁移概览

### 原始架构 (single_page.html)
- **技术栈**: Vue 2 + Element UI + CDN 依赖
- **文件结构**: 单个 HTML 文件包含所有代码
- **构建方式**: 无构建过程，直接在浏览器运行
- **依赖管理**: CDN 引入

### 现代架构 (Vue 3 + Vite)
- **技术栈**: Vue 3 + Element Plus + npm 依赖
- **文件结构**: 模块化组件结构
- **构建方式**: Vite 构建系统
- **依赖管理**: npm 包管理

## 主要变更

### 1. Vue 版本升级 (Vue 2 → Vue 3)

#### Options API → Composition API
```javascript
// Vue 2 (Options API)
export default {
  data() {
    return {
      cards: [],
      loading: true
    }
  },
  computed: {
    processedText() {
      return this.formatText(this.card.text)
    }
  },
  methods: {
    loadData() {
      // ...
    }
  }
}

// Vue 3 (Composition API)
export default {
  setup() {
    const cards = ref([])
    const loading = ref(true)
    
    const processedText = computed(() => {
      return formatText(card.text)
    })
    
    const loadData = () => {
      // ...
    }
    
    return {
      cards,
      loading,
      processedText,
      loadData
    }
  }
}
```

#### 模板语法变更
```html
<!-- Vue 2 -->
<template>
  <div slot="header">...</div>
  <div slot="error">...</div>
</template>

<!-- Vue 3 -->
<template>
  <template #header>...</template>
  <template #error>...</template>
</template>
```

### 2. UI 库升级 (Element UI → Element Plus)

#### 组件引入方式
```javascript
// Vue 2 + Element UI
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
Vue.use(ElementUI)

// Vue 3 + Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
app.use(ElementPlus)
```

#### 图标使用方式
```html
<!-- Element UI -->
<i class="el-icon-search"></i>
<i class="el-icon-picture-outline"></i>

<!-- Element Plus -->
<el-icon><Search /></el-icon>
<el-icon><Picture /></el-icon>
```

### 3. 项目结构重组

#### 文件拆分
```
single_page.html (2000+ 行)
├── HTML 结构
├── CSS 样式
├── JavaScript 逻辑
└── Vue 组件定义

↓ 拆分为 ↓

src/
├── App.vue (主应用组件)
├── components/WeiboCard.vue (微博卡片组件)
├── services/weiboService.js (API 服务)
├── utils/
│   ├── imageProxy.js (图片代理)
│   └── textFormatter.js (文本格式化)
└── styles/global.css (全局样式)
```

#### 组件化拆分
- **WeiboCard**: 独立的微博卡片组件
- **weiboService**: API 调用服务层
- **imageProxy**: 图片代理工具
- **textFormatter**: 文本格式化工具

### 4. 构建系统 (无构建 → Vite)

#### 依赖管理
```html
<!-- 原始版本: CDN 引入 -->
<script src="https://unpkg.com/vue@2.7.16/dist/vue.js"></script>
<script src="https://unpkg.com/element-ui@2.15.14/lib/index.js"></script>
```

```javascript
// 现代版本: npm 包管理
import Vue from 'vue'
import ElementPlus from 'element-plus'
```

#### 开发体验
- **热重载**: 代码修改后自动刷新
- **模块化**: ES6 模块系统
- **类型检查**: 更好的开发时错误提示
- **构建优化**: 代码分割、压缩等

## 迁移步骤

### 1. 环境准备
```bash
# 安装 Node.js 和 npm
npm init vue@latest weibo-app
cd weibo-app
npm install
```

### 2. 依赖迁移
```bash
# 安装 Vue 3 相关依赖
npm install vue@^3.4.0
npm install element-plus @element-plus/icons-vue
npm install axios dompurify luxon

# 安装开发依赖
npm install -D vite @vitejs/plugin-vue
```

### 3. 代码迁移
1. **提取组件**: 将 Vue 组件从 HTML 中提取到 `.vue` 文件
2. **转换语法**: Options API 转换为 Composition API
3. **更新模板**: 修改插槽语法和事件绑定
4. **拆分逻辑**: 将工具函数提取到独立模块

### 4. 样式迁移
1. **提取 CSS**: 将样式从 `<style>` 标签提取到独立文件
2. **保持兼容**: 确保样式在新架构下正常工作
3. **响应式适配**: 验证移动端和桌面端显示

### 5. 功能验证
1. **核心功能**: 微博展示、搜索、无限滚动
2. **交互功能**: 图片预览、返回顶部
3. **响应式**: 不同屏幕尺寸下的显示效果

## 兼容性说明

### 保持的功能
- ✅ 微博卡片展示
- ✅ 无限滚动加载
- ✅ 搜索功能
- ✅ 图片预览
- ✅ 转发内容显示
- ✅ 响应式设计
- ✅ 返回顶部按钮

### 改进的功能
- 🚀 更快的开发热重载
- 🚀 更好的代码组织
- 🚀 更强的类型安全
- 🚀 更优的构建性能
- 🚀 更现代的开发体验

## 注意事项

1. **API 兼容性**: 确保后端 API 接口保持兼容
2. **浏览器支持**: Vue 3 需要更现代的浏览器支持
3. **依赖更新**: 定期更新依赖包以获得安全修复
4. **构建配置**: 根据部署环境调整 Vite 配置

## 回滚方案

如果需要回滚到原始版本，可以直接使用 `examples/single_page.html` 文件，它包含了完整的原始功能。