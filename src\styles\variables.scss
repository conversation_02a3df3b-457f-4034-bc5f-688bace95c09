// SCSS 变量系统 - 设计令牌

// 颜色系统
$primary-color: #1677ff;
$primary-hover: #4096ff;
$primary-light: #e6f4ff;

// 文字颜色
$text-primary: #1d2129;
$text-secondary: #4e5969;
$text-tertiary: #86909c;
$text-disabled: #c9cdd4;

// 背景色
$bg-primary: #ffffff;
$bg-secondary: #f7f8fa;
$bg-tertiary: #f2f3f5;
$bg-overlay: rgba(0, 0, 0, 0.6);

// 边框色
$border-light: #f0f0f0;
$border-default: #d9d9d9;
$border-dark: #bfbfbf;

// 阴影系统
$shadow-1: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
$shadow-2: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02), 0 4px 12px 4px rgba(0, 0, 0, 0.03);
$shadow-3: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-hover: 0 8px 25px rgba(0, 0, 0, 0.08);

// 圆角系统
$radius-sm: 6px;
$radius-md: 8px;
$radius-lg: 12px;
$radius-xl: 16px;

// 间距系统
$space-xs: 4px;
$space-sm: 8px;
$space-md: 12px;
$space-lg: 16px;
$space-xl: 20px;
$space-2xl: 24px;

// 动画系统
$transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
$transition-base: 0.25s cubic-bezier(0.4, 0, 0.2, 1);
$transition-slow: 0.35s cubic-bezier(0.4, 0, 0.2, 1);

// 字体系统
$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
$font-size-xs: 12px;
$font-size-sm: 13px;
$font-size-base: 14px;
$font-size-md: 15px;
$font-size-lg: 16px;
$font-size-xl: 18px;
$font-size-2xl: 20px;

// 行高系统
$line-height-tight: 1.4;
$line-height-base: 1.6;
$line-height-relaxed: 1.7;

// 断点系统
$breakpoint-mobile: 768px;
$breakpoint-tablet: 1024px;
$breakpoint-desktop: 1200px;

// Z-index 系统
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// 暗色模式变量
$dark-text-primary: #e5e6eb;
$dark-text-secondary: #b0b3b8;
$dark-text-tertiary: #8a8d91;
$dark-text-disabled: #65676b;

$dark-bg-primary: #242526;
$dark-bg-secondary: #3a3b3c;
$dark-bg-tertiary: #4e4f50;
$dark-bg-overlay: rgba(0, 0, 0, 0.8);

$dark-border-light: #3e4042;
$dark-border-default: #5c5e61;
$dark-border-dark: #8a8d91;

$dark-shadow-1: 0 1px 2px 0 rgba(0, 0, 0, 0.3), 0 1px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px 0 rgba(0, 0, 0, 0.2);
$dark-shadow-2: 0 1px 2px 0 rgba(0, 0, 0, 0.3), 0 1px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px 0 rgba(0, 0, 0, 0.2), 0 4px 12px 4px rgba(0, 0, 0, 0.3);
$dark-shadow-3: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
$dark-shadow-hover: 0 8px 25px rgba(0, 0, 0, 0.4);