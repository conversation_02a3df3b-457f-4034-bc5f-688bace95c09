{"name": "weibo-vue-app", "version": "1.0.0", "description": "Modern Vue.js Weibo viewer application", "type": "module", "main": "index.js", "scripts": {"dev": "vite --mode development", "dev:test": "vite --mode test", "build": "vite build --mode production", "build:test": "vite build --mode test", "build:dev": "vite build --mode development", "preview": "vite preview", "serve": "vite preview", "vercel-build": "vite build --mode production"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.8", "dompurify": "^3.2.3", "element-plus": "^2.8.0", "luxon": "^3.5.0", "vue": "^3.4.0", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "sass": "^1.89.2", "sass-embedded": "^1.89.2", "vite": "^5.0.0"}}