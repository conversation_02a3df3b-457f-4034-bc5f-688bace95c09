# OAuth 配置说明

## 环境变量配置

本项目使用环境变量来管理 OAuth 配置，支持不同环境的配置。

### 配置文件

- `.env` - 默认配置
- `.env.development` - 开发环境配置
- `.env.test` - 测试环境配置  
- `.env.production` - 生产环境配置
- `.env.local` - 本地配置（优先级最高，不会被提交到版本控制）

### 必需的环境变量

```bash
# OAuth 配置
VITE_OAUTH_APPID=your_wechat_appid_here
VITE_OAUTH_AGENTID=your_wechat_agentid_here
VITE_OAUTH_SCOPE=snsapi_base
VITE_OAUTH_STATE=your_random_state_string_here
```

### 配置步骤

1. **复制配置模板**
   ```bash
   cp .env.example .env.local
   ```

2. **填入真实配置**
   编辑 `.env.local` 文件，替换占位符为真实值：
   - `VITE_OAUTH_APPID`: 微信企业应用的 AppID
   - `VITE_OAUTH_AGENTID`: 微信企业应用的 AgentID
   - `VITE_OAUTH_STATE`: 随机字符串，建议使用 UUID 或其他随机生成的字符串

3. **验证配置**
   启动应用后，访问 `/oauth?debug` 可以查看当前配置是否正确。

### 安全注意事项

1. **不要在代码中硬编码敏感信息**
2. **使用随机的 state 参数防止 CSRF 攻击**
3. **不同环境使用不同的配置值**
4. **确保 `.env.local` 文件不被提交到版本控制**

### 故障排除

1. **配置无效错误**
   - 检查环境变量是否正确设置
   - 确保变量名以 `VITE_` 开头
   - 重启开发服务器

2. **OAuth 跳转失败**
   - 检查 redirect_uri 是否与微信后台配置一致
   - 确认 AppID 和 AgentID 正确

3. **调试模式**
   - 访问 `/oauth?debug` 查看详细配置信息
   - 检查浏览器控制台的错误信息