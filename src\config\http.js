import axios from 'axios'
import { API_CONFIG, isDevelopment } from './api.js'

// 创建 axios 实例
const httpClient = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
httpClient.interceptors.request.use(
  (config) => {
    // 在开发环境下打印请求信息
    if (isDevelopment()) {
      console.log('🚀 API Request:', {
        method: config.method?.toUpperCase(),
        url: config.url,
        baseURL: config.baseURL,
        params: config.params,
        data: config.data
      })
    }
    
    // 添加认证信息 - 使用后端返回的令牌
    const authToken = localStorage.getItem('auth_token')
    if (authToken) {
      config.headers['Authorization'] = `Bearer ${authToken}`
    }
    
    return config
  },
  (error) => {
    console.error('❌ Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
httpClient.interceptors.response.use(
  (response) => {
    // 在开发环境下打印响应信息
    if (isDevelopment()) {
      console.log('✅ API Response:', {
        status: response.status,
        url: response.config.url,
        data: response.data
      })
    }
    
    return response
  },
  (error) => {
    console.error('❌ Response Error:', {
      status: error.response?.status,
      message: error.message,
      url: error.config?.url,
      data: error.response?.data
    })
    
    // 统一错误处理
    if (error.response?.status === 401) {
      // 处理未授权错误 - 清理本地认证信息并跳转到认证流程
      console.warn('🔐 Unauthorized access, clearing auth data')
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_info')
      
      // 触发重新认证
      const backUrl = encodeURIComponent(window.location.href)
      const authUrl = `${API_CONFIG.BASE_URL}/api/oauth/start?backto=${backUrl}`
      window.location.href = authUrl
    } else if (error.response?.status >= 500) {
      // 处理服务器错误
      console.error('🔥 Server error')
    }
    
    return Promise.reject(error)
  }
)

export default httpClient