<template>
  <div class="home">
    <div class="hero-section">
      <h1>我的关注列表</h1>
      <p v-if="isAuthenticated">查看您关注的用户</p>
      <p v-else>请先完成身份认证后使用</p>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-section">
      <el-alert title="加载中..." type="info" show-icon />
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error-section">
      <el-alert :title="error" type="error" show-icon />
    </div>
    
    <!-- 关注列表 -->
    <div v-if="isAuthenticated && !loading && !error" class="user-list">
      <div v-if="followedUsers.length > 0" class="user-cards">
        <div 
          v-for="user in followedUsers" 
          :key="user.id"
          class="user-card"
          @click="goToUser(user.id)"
        >
          <img 
            :src="getProxyImageUrl(user.profile_image_url)" 
            alt="头像" 
            class="user-avatar"
            @click.stop="showAvatar(user.avatar_hd)"
          />
          <div class="user-info">
            <h3 class="user-name" :title="user.screen_name">{{ user.screen_name }}</h3>
            <div class="user-stats">
              <span class="stat-tag comment">
                <i class="stat-icon">💬</i>
                {{ formatNumber(user.status_total_counter?.comment_cnt || 0) }}
              </span>
              <span class="stat-tag like">
                <i class="stat-icon">👍</i>
                {{ formatNumber(user.status_total_counter?.like_cnt || 0) }}
              </span>
              <span class="stat-tag repost">
                <i class="stat-icon">🔄</i>
                {{ formatNumber(user.status_total_counter?.repost_cnt || 0) }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="empty-section">
        <el-empty description="暂无关注" />
      </div>
    </div>

    <!-- 未认证提示 -->
    <div v-else-if="!isAuthenticated && !loading" class="auth-section">
      <el-card class="auth-card">
        <h3>需要身份认证</h3>
        <p>为了保护用户隐私和数据安全，请先完成身份认证。</p>
        <el-button type="primary" size="large" @click="goToAuth">
          开始认证
        </el-button>
      </el-card>
    </div>

    <!-- 头像预览对话框 -->
    <el-dialog v-model="dialogVisible" class="avatar-dialog">
      <img v-if="dialogImageUrl" :src="dialogImageUrl" alt="大图" style="width: 100%; display: block;" />
    </el-dialog>
    
    <!-- 开发环境认证测试组件 -->
    <AuthTest v-if="isDev" />
  </div>
</template>

<script>
import { ref, onMounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '../stores/auth.js'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import { getApiUrl } from '../config/api.js'
import AuthTest from '../components/AuthTest.vue'

export default {
  name: 'Home',
  components: {
    AuthTest
  },
  setup() {
    const router = useRouter()
    const { isAuthenticated, logout, getAuthHeaders, refreshAuth } = useAuth()
    
    // 状态管理
    const followedUsers = ref([])
    const loading = ref(false)
    const error = ref(null)
    
    // 对话框状态
    const dialogVisible = ref(false)
    const dialogImageUrl = ref('')
    
    // 开发环境标识
    const isDev = import.meta.env.DEV
    
    // 获取关注列表
    const fetchFollowedUsers = async () => {
      if (!isAuthenticated.value) {
        console.log('🔒 Not authenticated, skipping fetch')
        return
      }
      
      loading.value = true
      error.value = null
      
      try {
        // 使用 auth store 提供的方法获取认证头部
        const authHeaders = getAuthHeaders()
        
        if (!authHeaders.Authorization) {
          console.warn('⚠️ No auth token available')
          ElMessage.warning('认证信息缺失，请重新登录')
          logout()
          goToAuth()
          return
        }
        
        console.log('📡 Fetching followed users with auth headers')
        const response = await axios.get(getApiUrl('/api/get_subscribe'), {
          headers: authHeaders
        })
        
        if (response.data) {
          if (response.data.errcode === 0) {
            followedUsers.value = response.data.data || []
            console.log('✅ Successfully fetched followed users:', followedUsers.value.length)
          } else if (response.data.errcode === 1) {
            // 需要重新认证
            ElMessage.warning('认证已过期，请重新登录')
            logout()
            goToAuth()
          } else {
            error.value = response.data.message || '数据加载失败，请稍后重试。'
          }
        } else {
          error.value = '数据加载失败，请稍后重试。'
        }
      } catch (err) {
        console.error('Error fetching followed users:', err)
        if (err.response?.status === 401) {
          ElMessage.warning('认证已过期，请重新登录')
          logout()
          goToAuth()
        } else {
          error.value = '网络错误，请检查您的网络连接。'
        }
      } finally {
        loading.value = false
      }
    }
    
    // 图片代理
    const getProxyImageUrl = (url) => {
      if (!url) return ''
      return `https://image.baidu.com/search/down?url=${encodeURIComponent(url)}`
    }
    
    // 显示头像大图
    const showAvatar = (url) => {
      dialogImageUrl.value = getProxyImageUrl(url)
      dialogVisible.value = true
    }
    
    // 跳转到用户页面
    const goToUser = (userId) => {
      if (isAuthenticated.value) {
        ElMessage.success(`目标用户: ${userId}`)
        setTimeout(() => {
          router.push(`/user/${userId}`)
        }, 500)
      } else {
        ElMessage.warning('请先完成身份认证')
      }
    }
    
    // 跳转到认证页面
    const goToAuth = () => {
      const { startAuth } = useAuth()
      startAuth()
    }
    
    // 格式化数字显示 - 科学计数法
    const formatNumber = (num) => {
      // 处理可能包含逗号的字符串数字
      let cleanNum = num
      if (typeof num === 'string') {
        cleanNum = num.replace(/,/g, '')
      }
      
      // 确保输入是数字类型
      const number = Number(cleanNum)
      console.log('formatNumber called with:', num, 'cleaned:', cleanNum, 'converted to:', number)
      
      if (isNaN(number)) {
        console.log('Invalid number, returning original:', num)
        return num.toString()
      }
      
      if (number >= 10000000) {
        // 大于等于1000万，显示为 xxM
        const result = (number / 1000000).toFixed(1).replace(/\.0$/, '') + 'M'
        console.log('Formatted to M:', result)
        return result
      } else if (number >= 10000) {
        // 大于等于1万，显示为 xxK
        const result = (number / 1000).toFixed(1).replace(/\.0$/, '') + 'K'
        console.log('Formatted to K (10k+):', result)
        return result
      } else if (number >= 1000) {
        // 大于等于1千，显示为 x.xK
        const result = (number / 1000).toFixed(1).replace(/\.0$/, '') + 'K'
        console.log('Formatted to K (1k+):', result)
        return result
      }
      console.log('No formatting needed:', number.toString())
      return number.toString()
    }
    
    // 监听认证状态变化
    watch(isAuthenticated, async (newValue, oldValue) => {
      console.log('🔄 Auth status changed:', { from: oldValue, to: newValue })
      
      if (newValue && !oldValue) {
        // 从未认证变为已认证，等待下一个 tick 确保状态完全更新
        await nextTick()
        console.log('✅ Auth status changed to authenticated, fetching data...')
        fetchFollowedUsers()
      } else if (!newValue && oldValue) {
        // 从已认证变为未认证，清空数据
        console.log('🔒 Auth status changed to unauthenticated, clearing data...')
        followedUsers.value = []
        error.value = null
      }
    }, { immediate: false })
    
    // 组件挂载时的初始化
    onMounted(async () => {
      console.log('🏠 Home component mounted, auth status:', isAuthenticated.value)
      
      // 强制刷新认证状态，确保与 localStorage 同步
      refreshAuth()
      
      // 等待一个 tick 确保所有状态都已初始化
      await nextTick()
      
      // 如果已认证，则获取关注列表
      if (isAuthenticated.value) {
        console.log('�  Already authenticated on mount, fetching data...')
        fetchFollowedUsers()
      } else {
        console.log('🔒 Not authenticated on mount')
      }
    })
    
    return {
      // 状态
      followedUsers,
      loading,
      error,
      isAuthenticated,
      dialogVisible,
      dialogImageUrl,
      isDev,
      
      // 方法
      goToUser,
      goToAuth,
      getProxyImageUrl,
      showAvatar,
      formatNumber
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  max-width: 800px;
  margin: 0 auto;
  padding: $space-2xl $space-lg 0;
  position: relative;
  
  @media (max-width: #{$breakpoint-mobile - 1px}) {
    padding: $space-xl $space-md 0;
  }
}

.hero-section {
  text-align: center;
  margin-bottom: $space-2xl;
  padding: $space-2xl 0;
  
  h1 {
    font-size: $font-size-2xl;
    color: $text-primary;
    margin-bottom: $space-md;
    font-weight: 600;
    
    @include mobile {
      font-size: $font-size-xl;
    }
  }
  
  p {
    font-size: $font-size-lg;
    color: $text-secondary;
    margin: 0;
    
    @include mobile {
      font-size: $font-size-base;
    }
  }
}

.auth-section {
  padding: $space-2xl 0;
  text-align: center;
  
  .auth-card {
    max-width: 400px;
    margin: 0 auto;
    @include card-base;
    padding: $space-2xl;
    
    h3 {
      margin-bottom: $space-lg;
      color: $text-primary;
    }
    
    p {
      margin-bottom: $space-xl;
      color: $text-secondary;
      line-height: $line-height-relaxed;
    }
  }
}

.user-list {
  .user-cards {
    display: grid;
    gap: $space-lg;
    
    // 桌面端：3列布局
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    
    // 平板端：2列布局
    @media (max-width: #{$breakpoint-tablet - 1px}) and (min-width: 600px) {
      grid-template-columns: repeat(2, 1fr);
      gap: $space-md;
    }
    
    // 大屏手机端：2列布局（宽度足够时）
    @media (max-width: 599px) and (min-width: 480px) {
      grid-template-columns: repeat(2, 1fr);
      gap: $space-sm;
    }
    
    // 小屏手机端：1列布局
    @media (max-width: 479px) {
      grid-template-columns: 1fr;
      gap: $space-md;
    }
  }
  
  .user-card {
    @include card-base;
    @include card-top-accent;
    padding: $space-lg;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all $transition-base;
    position: relative;
    min-height: 80px;
    
    &:hover {
      @include card-hover;
      border-color: $primary-color;
    }
    
    &:active {
      @include card-active;
    }
    
    // 移动端优化
    @media (max-width: 599px) {
      padding: $space-md;
      min-height: 70px;
      
      // 2列布局时的特殊样式
      @media (min-width: 480px) {
        flex-direction: column;
        padding: $space-md $space-sm;
        min-height: 120px;
        align-items: stretch;
        justify-content: flex-start;
      }
    }
  }
  
  .user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: $space-lg;
    object-fit: cover;
    cursor: pointer;
    border: 3px solid $border-light;
    transition: all $transition-base;
    flex-shrink: 0;
    
    &:hover {
      border-color: $primary-color;
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(22, 119, 255, 0.2);
    }
    
    // 移动端横向布局
    @media (max-width: 599px) and (min-width: 480px) {
      width: 56px;
      height: 56px;
      margin-right: 0;
      margin-bottom: $space-sm;
    }
    
    // 移动端纵向布局
    @media (max-width: 479px) {
      width: 52px;
      height: 52px;
      margin-right: $space-md;
    }
  }
  
  .user-info {
    flex-grow: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: $space-sm;
    align-items: flex-start;
    
    // 移动端2列布局时也保持左对齐
    @media (max-width: 599px) and (min-width: 480px) {
      width: 100%;
      gap: $space-xs;
      align-items: flex-start;
      justify-content: flex-start;
    }
  }
  
  .user-name {
    font-weight: 600;
    color: $text-primary;
    margin: 0;
    font-size: $font-size-lg;
    line-height: $line-height-tight;
    word-break: break-all;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-align: left;
    align-self: flex-start;
    width: 100%;
    
    // 移动端2列布局时
    @media (max-width: 599px) and (min-width: 480px) {
      font-size: $font-size-base;
      -webkit-line-clamp: 2;
      text-align: left;
      align-self: flex-start;
    }
    
    // 小屏移动端
    @media (max-width: 479px) {
      font-size: $font-size-base;
      -webkit-line-clamp: 1;
    }
  }
  
  .user-stats {
    display: flex;
    gap: $space-xs;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-self: flex-start;
    width: 100%;
    
    .stat-tag {
      display: inline-flex;
      align-items: center;
      gap: 2px;
      padding: 3px 8px;
      border-radius: $radius-lg;
      font-size: $font-size-xs;
      font-weight: 500;
      white-space: nowrap;
      transition: all $transition-fast;
      
      .stat-icon {
        font-size: 12px;
        line-height: 1;
      }
      
      &.comment {
        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
        color: #1976d2;
        border: 1px solid #e3f2fd;
        
        &:hover {
          background: linear-gradient(135deg, #bbdefb, #90caf9);
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
        }
      }
      
      &.like {
        background: linear-gradient(135deg, #fce4ec, #f8bbd9);
        color: #c2185b;
        border: 1px solid #fce4ec;
        
        &:hover {
          background: linear-gradient(135deg, #f8bbd9, #f48fb1);
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(194, 24, 91, 0.2);
        }
      }
      
      &.repost {
        background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
        color: #388e3c;
        border: 1px solid #e8f5e8;
        
        &:hover {
          background: linear-gradient(135deg, #c8e6c9, #a5d6a7);
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(56, 142, 60, 0.2);
        }
      }
    }
    
    @media (max-width: 599px) {
      gap: 4px;
      
      .stat-tag {
        padding: 2px 6px;
        font-size: 10px;
        
        .stat-icon {
          font-size: 10px;
        }
      }
    }
  }
}

.loading-section,
.error-section {
  padding: $space-xl 0;
  text-align: center;
  
  @include mobile {
    padding: $space-lg 0;
  }
}

.empty-section {
  padding: $space-2xl 0;
  text-align: center;
  
  @include mobile {
    padding: $space-xl 0;
  }
}

// 卡片进入动画
.user-card {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
  
  @for $i from 1 through 20 {
    &:nth-child(#{$i}) {
      animation-delay: #{($i - 1) * 0.1}s;
    }
  }
}

// 动画关键帧
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 头像对话框样式
.avatar-dialog :deep(.el-dialog__body) {
  padding: 0;
}

// 触摸设备优化
@media (hover: none) and (pointer: coarse) {
  .user-card {
    &:active {
      transform: scale(0.98);
      transition: transform 0.1s ease-out;
    }
  }
  
  .user-avatar {
    &:active {
      transform: scale(0.95);
    }
  }
}

// 高分辨率屏幕优化
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .user-avatar {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}
</style>