<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="https://m.weibo.cn/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="https://unpkg.com/element-ui@2.15.14/lib/theme-chalk/index.css">
    <style>
        /* 根样式，定义主题色 */
        :root {
            --primary-color: #0084ff;
            --secondary-color: #606266;
            --text-color: #303133;
            --border-color: #e4e7ed;
            --background-color: #f2f3f5;
            --card-background: #fff;
            --shadow-color: rgba(0, 0, 0, 0.05);
            --hover-shadow-color: rgba(0, 0, 0, 0.12);
        }
    
        /* 统一容器样式 */
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 15px;
        }
    
        /* 搜索框样式 */
        .search-box {
            background: var(--card-background);
            padding: 70px 0 20px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 8px var(--shadow-color);
        }
    
        /* 卡片样式 */
        .box-card {
            width: 100%;
            margin: 0 0;
            border-radius: 12px;
            border: none;
            transition: transform 0.2s, box-shadow 0.2s;
            background: var(--card-background);
        }
    
        .box-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px var(--hover-shadow-color);
        }

        .box-card .el-card__body {
            margin: 0;
            padding: 12px 0;
        }
    
        /* 卡片头部 */
        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
        }
    
        .card-header>div {
            display: flex;
            align-items: center;
        }
    
        .card-title {
            margin-left: 8px;
        }
    
        .user-name {
            font-weight: 600;
            color: var(--text-color);
            font-size: 12px;
        }
    
        /* 正文内容 */
        .card-content {
            line-height: 1.6;
            color: var(--text-color);
            padding: 0 20px;
            font-size: 15px;
        }
    
        .card-content > .inline-emoji {
            display: inline-block;
            vertical-align: middle;
            height: 1em;
            width: auto;
            margin-bottom: 0.2em;
        }

        .card-content a {
            color: var(--primary-color);
            text-decoration: none;
        }

        /* 图片展示 */
        .image-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            padding: 12px 20px;
        }
    
        .image-grid .el-image {
            width: calc(33.33% - 8px);
            height: auto;
            aspect-ratio: 1 / 1;
            border-radius: 6px;
            background: var(--background-color);
            transition: transform 0.2s;
            object-fit: cover;
        }
    
        .el-image:hover {
            transform: scale(0.98);
        }
    
        /* 转发内容 */
        .retweet-card {
            margin: 16px 0;
            padding: 16px;
            background: var(--background-color);
            border-radius: 8px;
        }
    
        /* 操作按钮 */
        .card-actions {
            padding: 12px 20px;
            border-top: 1px solid var(--border-color);
            text-align: right;
        }
    
        /* 加载更多 */
        .load-more-button-container {
            padding: 30px 0;
            text-align: center;
        }
    
        .no-more {
            color: var(--secondary-color);
            font-size: 14px;
        }
    
        /* 返回顶部 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 40px;
            height: 40px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            box-shadow: 0 4px 12px rgba(0, 132, 255, 0.3);
            cursor: pointer;
            opacity: 0.9;
            transition: 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
    
        .back-to-top:hover {
            opacity: 1;
            transform: translateY(-2px);
        }
    
        /* 话题标签样式 */
        .topic-tag {
            color: var(--primary-color);
            padding: 0 0.3em;
        }
    
        /* @ 用户的标签样式 */
        .at-user-tag {
            color: var(--primary-color);
            padding: 0 0.3em;
        }
    
        
    
        /* 修改后的post-info样式 */
        .post-info {
            color: #8590a6;
            font-size: 8px;
            text-align: right;
            display: flex;
            flex-direction: column;
            gap: 2px;
            /* 确保 align-items 应用到 flex 容器上 */
            align-items: flex-end;
        }
    
        /* 确保 .post-info 是 card-header 的直接子元素，并且没有其他样式覆盖它 */
        .card-header>.post-info {
            align-items: flex-end;
        }
    
        .post-info span {
            white-space: nowrap;
        }
    
        /* 确保头像为正圆 */
        .el-avatar {
            width: 30px !important;
            height: 30px !important;
            border-radius: 50%;
            overflow: hidden;
        }
    
        /* 电脑端样式覆盖 */
        @media (min-width: 768px) {
            .user-name {
                font-size: 14px; /* 电脑端正常大小 */
            }
    
            .post-info {
                font-size: 12px; /* 电脑端正常大小 */
            }

            .box-card .el-card__body {
                padding: 20px 0;
                margin: 0 20px;
            }

            .card-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 12px 20px;
            }
        }

        .infinite-list {
            list-style: none;  /* 移除列表项的点 */
            padding: 0;        /* 移除默认的内边距 */
            margin: 0;         /* 移除默认的外边距 */
        }
    </style>
</head>


<body>
    <div id="app">
        <!-- 搜索框 -->
        <div class="search-box">
            <div class="container">
                <el-input v-model="keyword" placeholder="搜索微博内容..." clearable @keyup.enter.native="handleSearch">
                    <i slot="prefix" class="el-input__icon el-icon-search"></i>
                </el-input>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="container">
            <!-- 加载状态 -->
            <div v-if="loading" class="loading-container">
                <el-skeleton :rows="3" animated />
            </div>

            <!-- 微博卡片 -->
            <template v-else>
                <ul class="infinite-list" v-infinite-scroll="load" style="overflow:visible">
                    <li v-for="(card, index) in cards" :key="card.id" class="card-observer infinite-list-item" :data-index="index">
                        <weibo-card :card="card" :is-visible="card.isCardVisible"></weibo-card>
                    </li>
                </ul>

                <div v-if="!nextCursor && cards.length" class="no-more">
                    —— 已显示全部内容 ——
                </div>
            </template>
        </div>

        <!-- 返回顶部 -->
        <button class="back-to-top" @click="scrollToTop" v-show="showBackTop">
            ↑
        </button>
    </div>

    <!-- 微博卡片模板 -->
    <template id="weibo-card-template">
        <el-card class="box-card">
            <!-- 头部 -->
            <div slot="header" class="card-header">
                <div>
                    <el-avatar :src="avatarUrl"></el-avatar>
                    <div class="card-title">
                        <div class="user-name">{{ card.user.screen_name }}</div>
                    </div>
                </div>
                <div class="post-info">
                    <span class="post-date">{{ formattedDate }}</span>
                    <span v-html="formatText(card.source)"></span>
                    <span v-if="card.region_name">{{ card.region_name }}</span>
                </div>
            </div>

            <!-- 正文 -->
            <div class="card-content" v-html="processedText"></div>

            <!-- 图片 -->
            <div class="image-grid">
                <el-image v-for="(url, index) in visibleImages" :key="index" :src="url" fit="cover" lazy
                    :preview-src-list="visibleImages">
                    <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                    </div>
                </el-image>
            </div>

            <!-- 转发内容 -->
            <div v-if="card.retweeted_status" class="retweet-card">
                <div class="retweet-header">
                    <span class="at-user-tag">@{{ retweetedName }}</span>:
                </div>
                <div class="card-content" v-html="retweetedText"></div>

                <div class="image-grid" :class="retweetedImageGridClass">
                    <el-image v-for="(url, index) in retweetedImages" :key="index" :src="url" fit="cover" lazy
                        :preview-src-list="retweetedImages">
                        <div slot="error" class="image-slot">
                            <i class="el-icon-picture-outline"></i>
                        </div>
                    </el-image>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="card-actions">
                <el-button type="primary" plain round size="mini" @click="openWeibo">
                    <i class="el-icon-link"></i>查看原文
                </el-button>
            </div>
        </el-card>
    </template>

    <!-- 依赖引入 -->
    <script src="https://unpkg.com/vue@2.7.16/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui@2.15.14/lib/index.js"></script>
    <script src="https://unpkg.com/axios@1.6.8/dist/axios.min.js"></script>
    <script src="https://unpkg.com/dompurify@3.2.3/dist/purify.min.js"></script>
    <script src="https://unpkg.com/luxon@3.5.0/build/global/luxon.min.js"></script>
    <script>
        // 微博卡片组件
        Vue.component('weibo-card', {
            template: '#weibo-card-template',
            props: {
                card: {
                    type: Object,
                    required: true
                },
                isVisible: {
                    type: Boolean,
                    default: false
                }
            },
            computed: {
                // 统一图片代理
                proxyImage() {
                    return (url) => {
                        if (!url) return '';
                        // [保留此注释]: https://i0.wp.com/wx1.sinaimg.cn/large/001O3KAmgy1how1ilh7fnj63lp5eg7wl02.jpg
                        // [保留此注释]: https://www.codenong.com/getimg_baidu.php?url=https://wx1.sinaimg.cn/large/001O3KAmgy1how1ilh7fnj63lp5eg7wl02.jpg
                        return `https://image.baidu.com/search/down?url=${encodeURIComponent(url)}`;
                    }
                },
                // 头像
                avatarUrl() {
                    return this.proxyImage(this.card.user.profile_image_url);
                },
                // 处理后的正文
                processedText() {
                    return this.formatText(this.card.text);
                },
                // 格式化时间
                formattedDate() {
                    const cardTime = new Date(this.card.created_at);
                    const dateTime = luxon.DateTime.fromMillis(cardTime.getTime());
                    return dateTime.setLocale('zh').toFormat('yyyy年MM月dd日 HH:mm:ss');
                },
                // 微博链接
                weiboUrl() {
                    return `https://m.weibo.cn/detail/${this.card.id}`;
                },
                // 转发的用户昵称
                retweetedName() {
                    return this.card.retweeted_status?.user?.screen_name || '已删除用户';
                },
                // 转发的文本
                retweetedText() {
                    return this.formatText(this.card.retweeted_status?.text);
                },
                // 主图列表
                visibleImages() {
                    if (this.card.pic_infos) {
                        return this.getImageUrlsInfo(this.card.pic_infos).map(url => this.proxyImage(url));
                    } else if (this.card.pics) {
                        console.log(this.card.pics);
                        return this.getImageUrlsList(this.card.pics).map(url => this.proxyImage(url));
                    }
                },
                // 转发图片列表
                retweetedImages() {
                    if (this.card.retweeted_status?.pic_infos) {
                        return this.getImageUrlsInfo(this.card.retweeted_status?.pic_infos).map(url => this.proxyImage(url));
                    } else if (this.card.retweeted_status?.pics) {
                        console.log(this.card.pics);
                        return this.getImageUrlsList(this.card.retweeted_status?.pics).map(url => this.proxyImage(url));
                    }
                },
                // 图片布局类名
                imageGridClass() {
                    return this.visibleImages.length > 1 ? 'multi-images' : 'single-image';
                },
                retweetedImageGridClass() {
                    return this.retweetedImages.length > 1 ? 'multi-images' : 'single-image';
                }
            },
            methods: {
                getImageUrlsInfo(picInfos) {
                    if (!picInfos) return [];
                    return Object.values(picInfos).map(pic => {
                        return pic.large?.url || pic.original?.url || '';
                    }).filter(Boolean);
                },
                getImageUrlsList(pics) {
                    if (!pics) return [];
                    return pics.map(pic => {
                        return pic.large?.url || pic.original?.url || pic.url || '';
                    }).filter(Boolean);
                },
                openWeibo() {
                    window.open(this.weiboUrl, '_blank');
                },
                formatText(text) {
                    if (!text) return '';

                    DOMPurify.addHook('beforeSanitizeElements', node => {
                        if (node.nodeType === 1 && node.tagName === 'A') {
                            const content = node.textContent.trim();
                            if (content.startsWith('#') && content.endsWith('#')) {
                                node.setAttribute('data-topic', 'true');
                            }
                            if (content.startsWith('@')) {
                                node.setAttribute('data-at-user', 'true');
                            }
                        }
                    });

                    const cleanHTML = DOMPurify.sanitize(text.trim(), {
                        ALLOWED_TAGS: ['a', 'img', 'br', 'span'],
                        ALLOWED_ATTR: ['src', 'data-topic', 'data-at-user', 'style'],
                    });

                    DOMPurify.removeHook('beforeSanitizeElements');

                    const div = document.createElement('div');
                    div.innerHTML = cleanHTML;

                    div.querySelectorAll('a[data-topic]').forEach(a => {
                        const span = document.createElement('span');
                        span.className = 'topic-tag';
                        span.innerHTML = a.innerHTML;
                        a.replaceWith(span);
                    });

                    div.querySelectorAll('a[data-at-user]').forEach(a => {
                        const span = document.createElement('span');
                        span.className = 'at-user-tag';
                        span.innerHTML = a.innerHTML;
                        a.replaceWith(span);
                    });

                    div.querySelectorAll('a:not([data-topic]):not([data-at-user])').forEach(a => {
                        a.replaceWith(...a.childNodes);
                    });

                    div.querySelectorAll('img').forEach(img => {
                        img.className = 'inline-emoji';
                        img.src = this.proxyImage(img.src);
                    });

                    return div.innerHTML;
                }
            },
            watch: {
                isVisible(newValue) {
                    if (newValue) {
                        //console.log('Card is visible:', this.card.id);
                    }
                }
            }
        });

        // 主Vue实例
        new Vue({
            el: '#app',
            data: {
                cards: [],
                loading: true,
                loadingMore: false,
                autoLoading: false,
                nextCursor: null,
                userId: null,
                keyword: '',
                showBackTop: false,
                searchTimer: null,
                scrollListener: null
            },
            mounted() {
                this.userId = this.getUserIdFromUrl();
                if (this.userId) {
                    this.loadData();
                    this.initScrollListener();
                } else {
                    this.$message.error('用户ID参数错误');
                    this.loading = false;
                }
            },
            methods: {
                initScrollListener() {
                    this.scrollListener = () => {
                        this.showBackTop = window.scrollY > 300;

                        if (this.loading || this.loadingMore || !this.nextCursor) return;

                        const { scrollTop, clientHeight, scrollHeight } = document.documentElement;
                        const scrollPosition = scrollTop + clientHeight;

                        if (scrollPosition >= scrollHeight - 500 && this.nextCursor) {
                            this.loadMore();
                        }
                    };
                    window.addEventListener('scroll', this.scrollListener);
                },
                getUserIdFromUrl() {
                    const match = location.pathname.match(/\/user\/(\d+)/);
                    return match?.[1] || null;
                },
                handleSearch() {
                    clearTimeout(this.searchTimer);
                    this.searchTimer = setTimeout(() => {
                        this.cards = [];
                        this.nextCursor = null;
                        this.loadData();
                    }, 300);
                },
                async loadData() {
                    try {
                        this.loading = true;
                        const params = {
                            user: this.userId,
                            limit: 5,
                            cursor: this.nextCursor,
                            keyword: this.keyword
                        };

                        const { data } = await axios.get('/api/tweets', { params });

                        if (data?.success) {
                            const newCards = data.data?.length ? data.data.map(card => ({
                                ...card,
                                isCardVisible: false
                            })) : [];

                            // this.cards = [...this.cards, ...newCards];
                            // newCards.forEach(card => {
                            //     this.cards.push(card);
                            // });
                            this.cards = this.cards.concat(newCards);
                            this.nextCursor = data.next_cursor || null;

                            if (!newCards.length && this.autoLoading) {
                                this.autoLoading = false;
                                this.$message.info('没有更多内容了');
                            }
                        } else {
                            this.nextCursor = null;
                            this.autoLoading = false;
                            this.loadingMore = false;
                            this.$message.info('没有更多内容了');
                        }
                    } catch (error) {
                        this.nextCursor = null;
                        console.error('加载错误:', error);
                    } finally {
                        this.loading = false;
                        this.loadingMore = false;
                    }
                },
                async loadMore() {
                    if (!this.nextCursor || this.loadingMore) {
                        this.loadingMore = false;
                        this.autoLoading = false;
                        return;
                    }

                    this.loadingMore = true;
                    this.autoLoading = true;
                    const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
                    await this.loadData();
                    Vue.nextTick(() => {
                        document.documentElement.scrollTop = scrollTop;
                        document.body.scrollTop = scrollTop;
                    });
                },
                scrollToTop() {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                }
            },
            beforeDestroy() {
                window.removeEventListener('scroll', this.scrollListener);
            }
        });
    </script>
</body>

</html>
