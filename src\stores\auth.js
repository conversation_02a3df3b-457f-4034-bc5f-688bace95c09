import { ref, computed } from 'vue'
import { authService } from '../services/authService.js'
import { getApiUrl, isDevelopment } from '../config/api.js'

// 全局认证状态 - 基于后端令牌的认证
const isAuthenticated = ref(false)
const userInfo = ref(null)
const authToken = ref(null)

// 初始化认证状态
const initAuth = () => {
  const savedToken = authService.getToken()
  const savedUserInfo = authService.getUserInfo()

  if (savedToken) {
    authToken.value = savedToken
    userInfo.value = savedUserInfo
    isAuthenticated.value = true

    if (isDevelopment()) {
      console.log('🔐 Auth initialized:', {
        hasToken: !!savedToken,
        tokenPreview: savedToken.substring(0, 10) + '...',
        userInfo: savedUserInfo
      })
    }
  } else {
    // 确保状态清空
    authToken.value = null
    userInfo.value = null
    isAuthenticated.value = false
    
    if (isDevelopment()) {
      console.log('🔒 Auth not initialized - no saved token')
    }
  }
}

// 处理后端认证回调 - 从 URL 参数中获取令牌和用户信息
const handleAuthCallback = () => {
  const urlParams = new URLSearchParams(window.location.search)
  const token = urlParams.get('token')
  const user = urlParams.get('user')
  const authSuccess = urlParams.get('auth_success')
  const authError = urlParams.get('auth_error')

  if (isDevelopment()) {
    console.log('🔍 Auth callback check:', {
      hasToken: !!token,
      hasUser: !!user,
      authSuccess,
      authError,
      currentUrl: window.location.href
    })
  }

  // 处理认证成功
  if (token) {
    let userInfoData = null
    if (user) {
      try {
        userInfoData = JSON.parse(decodeURIComponent(user))
      } catch (e) {
        console.warn('Failed to parse user info from URL:', e)
      }
    }

    const success = authService.setAuthenticated(token, userInfoData)
    if (success) {
      authToken.value = token
      userInfo.value = userInfoData
      isAuthenticated.value = true

      if (isDevelopment()) {
        console.log('✅ Auth callback success:', {
          token: token.substring(0, 10) + '...',
          userInfo: userInfoData
        })
      }

      // 清理 URL 参数 - History 模式下使用 pushState 避免页面刷新
      const cleanUrl = window.location.origin + window.location.pathname
      window.history.pushState({}, document.title, cleanUrl)

      return { success: true, message: '认证成功' }
    } else {
      console.error('❌ Failed to set authentication')
      return { success: false, message: '认证设置失败' }
    }
  }

  // 处理认证错误
  if (authError) {
    console.error('❌ Auth callback error:', authError)
    // 清理 URL 参数
    const cleanUrl = window.location.origin + window.location.pathname
    window.history.pushState({}, document.title, cleanUrl)
    return { success: false, message: `认证失败: ${decodeURIComponent(authError)}` }
  }

  // 处理认证成功标识（无令牌的情况）
  if (authSuccess === 'true') {
    // 清理 URL 参数
    const cleanUrl = window.location.origin + window.location.pathname
    window.history.pushState({}, document.title, cleanUrl)
    return { success: true, message: '认证成功' }
  }

  return { success: false, message: null }
}

// 开始认证流程 - 跳转到后端认证接口
const startAuth = () => {
  // 在 History 模式下，使用专门的回调路由
  const callbackUrl = window.location.origin + '/auth/callback'
  const backUrl = encodeURIComponent(callbackUrl)
  const authUrl = getApiUrl(`/api/oauth2?backto=${backUrl}`)
  const jumpUrl = getApiUrl(`/api/oauth2?redirect=${encodeURIComponent(authUrl)}`)

  if (isDevelopment()) {
    console.log('🚀 Starting auth flow:', {
      callbackUrl,
      backUrl: callbackUrl,
      authUrl,
      jumpUrl
    })
  }

  window.location.href = jumpUrl
}

// 设置认证状态 - 手动设置令牌和用户信息
const setAuth = (token, userInfoData) => {
  if (!token || !token.trim()) {
    console.warn('Invalid auth token provided to setAuth')
    return false
  }
  
  const success = authService.setAuthenticated(token, userInfoData)
  if (success) {
    authToken.value = token
    userInfo.value = userInfoData
    isAuthenticated.value = true
    
    if (isDevelopment()) {
      console.log('✅ Auth set manually:', {
        token: token.substring(0, 10) + '...',
        userInfo: userInfoData
      })
    }
    
    return true
  }
  return false
}

// 登出
const logout = () => {
  authToken.value = null
  userInfo.value = null
  isAuthenticated.value = false
  authService.logout()

  if (isDevelopment()) {
    console.log('👋 User logged out')
  }
}

// 获取认证头部，用于 API 请求
const getAuthHeaders = () => {
  return authService.getAuthHeader()
}

// 强制刷新认证状态 - 从 localStorage 重新加载
const refreshAuth = () => {
  if (isDevelopment()) {
    console.log('🔄 Refreshing auth state from localStorage...')
  }
  
  initAuth()
  
  if (isDevelopment()) {
    console.log('✅ Auth state refreshed:', {
      isAuthenticated: isAuthenticated.value,
      hasToken: !!authToken.value
    })
  }
}

// 导出认证状态和方法
export const useAuth = () => {
  return {
    // 状态
    isAuthenticated: computed(() => isAuthenticated.value),
    userInfo: computed(() => userInfo.value),
    authToken: computed(() => authToken.value),

    // 方法
    initAuth,
    handleAuthCallback,
    setAuth,
    startAuth,
    logout,
    getAuthHeaders,
    refreshAuth
  }
}