import httpClient from '../config/http.js'
import { API_CONFIG } from '../config/api.js'

class WeiboService {
  /**
   * 获取微博推文
   * @param {Object} params - 查询参数
   * @param {string} params.user - 用户ID
   * @param {number} params.limit - 限制数量
   * @param {string} params.cursor - 游标
   * @param {string} params.keyword - 关键词
   * @returns {Promise<Object>} 返回微博数据
   */
  async getTweets(params) {
    try {
      const { data } = await httpClient.get(API_CONFIG.ENDPOINTS.TWEETS, { 
        params 
      })
      return data
    } catch (error) {
      console.error('获取微博数据失败:', error)
      throw error
    }
  }

  /**
   * 获取用户信息
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 返回用户信息
   */
  async getUserInfo(userId) {
    try {
      const { data } = await httpClient.get(`${API_CONFIG.ENDPOINTS.USER}/${userId}`)
      return data
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  /**
   * 搜索微博
   * @param {Object} params - 搜索参数
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.limit - 限制数量
   * @param {string} params.cursor - 游标
   * @returns {Promise<Object>} 返回搜索结果
   */
  async searchTweets(params) {
    try {
      const { data } = await httpClient.get(API_CONFIG.ENDPOINTS.SEARCH, { 
        params 
      })
      return data
    } catch (error) {
      console.error('搜索微博失败:', error)
      throw error
    }
  }
}

export const weiboService = new WeiboService()