# 前端样式优化设计文档

## 概述

本设计文档详细描述了微博 Vue 应用前端样式优化的技术方案，包括架构设计、组件结构、样式系统和实现细节。

## 架构设计

### 整体架构

```
src/
├── styles/                 # 样式系统
│   ├── index.scss          # 主样式入口
│   ├── variables.scss      # SCSS 变量
│   ├── mixins.scss         # SCSS 混合
│   ├── base.scss           # 基础样式
│   ├── components.scss     # 组件样式
│   └── utilities.scss      # 工具类样式
├── components/
│   ├── layout/             # 布局组件
│   │   ├── AppLayout.vue   # 主布局
│   │   ├── AppHeader.vue   # 顶部导航
│   │   └── AppMain.vue     # 主内容区
│   └── ...                 # 其他业务组件
└── views/                  # 页面组件
```

### 样式系统架构

1. **变量系统**: 使用 SCSS 变量管理颜色、字体、间距等设计令牌
2. **混合系统**: 定义可复用的样式模式和动画效果
3. **组件样式**: 每个组件的样式模块化管理
4. **工具类**: 提供常用的原子化样式类

## 组件设计

### AppLayout 组件

**职责**: 提供应用的整体布局框架

**结构**:
```vue
<template>
  <div class="app-layout">
    <AppHeader />
    <main class="app-main">
      <router-view />
    </main>
  </div>
</template>
```

**样式特性**:
- 固定顶部导航
- 响应式主内容区
- 统一的页面容器

### AppHeader 组件

**职责**: 提供统一的顶部导航栏

**功能模块**:
1. **左侧**: Logo 和应用标题
2. **中间**: 导航菜单（如需要）
3. **右侧**: 用户状态和操作按钮

**状态管理**:
- 登录状态显示
- 用户信息展示
- 退出登录功能

### 页面组件重构

**Home 页面**:
- 移除独立的用户状态栏
- 使用统一的页面容器
- 优化关注列表的展示

**UserProfile 页面**:
- 统一导航栏样式
- 优化搜索框布局
- 改进微博卡片列表

**WeiboDetail 页面**:
- 统一错误状态处理
- 优化加载状态显示
- 改进返回导航

## 样式系统设计

### SCSS 变量系统

```scss
// 颜色系统
$primary-color: #1677ff;
$primary-hover: #4096ff;
$primary-light: #e6f4ff;

// 文字颜色
$text-primary: #1d2129;
$text-secondary: #4e5969;
$text-tertiary: #86909c;

// 背景色
$bg-primary: #ffffff;
$bg-secondary: #f7f8fa;
$bg-tertiary: #f2f3f5;

// 间距系统
$space-xs: 4px;
$space-sm: 8px;
$space-md: 12px;
$space-lg: 16px;
$space-xl: 20px;
$space-2xl: 24px;

// 圆角系统
$radius-sm: 6px;
$radius-md: 8px;
$radius-lg: 12px;
$radius-xl: 16px;

// 阴影系统
$shadow-1: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
$shadow-2: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 4px 12px 4px rgba(0, 0, 0, 0.03);
$shadow-3: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
$shadow-hover: 0 8px 25px rgba(0, 0, 0, 0.08);

// 动画系统
$transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
$transition-base: 0.25s cubic-bezier(0.4, 0, 0.2, 1);
$transition-slow: 0.35s cubic-bezier(0.4, 0, 0.2, 1);
```

### SCSS 混合系统

```scss
// 卡片样式混合
@mixin card-base {
  background: $bg-primary;
  border-radius: $radius-xl;
  border: 1px solid $border-light;
  box-shadow: $shadow-1;
  transition: all $transition-base;
}

@mixin card-hover {
  transform: translateY(-2px);
  box-shadow: $shadow-hover;
  border-color: $border-default;
}

// 按钮样式混合
@mixin button-base {
  border-radius: $radius-lg;
  font-weight: 500;
  transition: all $transition-base;
  cursor: pointer;
}

@mixin button-primary {
  background: $primary-color;
  color: white;
  border: 1px solid $primary-color;
  
  &:hover {
    background: $primary-hover;
    transform: translateY(-1px);
    box-shadow: $shadow-2;
  }
}

// 响应式混合
@mixin mobile {
  @media (max-width: 768px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 769px) and (max-width: 1024px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1025px) {
    @content;
  }
}
```

## 数据模型

### 布局状态模型

```typescript
interface LayoutState {
  headerHeight: number;
  sidebarCollapsed: boolean;
  loading: boolean;
  error: string | null;
}
```

### 主题配置模型

```typescript
interface ThemeConfig {
  primaryColor: string;
  darkMode: boolean;
  fontSize: 'small' | 'medium' | 'large';
  compactMode: boolean;
}
```

## 错误处理

### 样式加载错误

1. **SCSS 编译错误**: 提供详细的错误信息和修复建议
2. **样式冲突**: 使用 CSS 模块化避免样式冲突
3. **浏览器兼容性**: 提供降级方案和 polyfill

### 布局错误处理

1. **组件加载失败**: 显示错误边界组件
2. **响应式布局问题**: 提供最小宽度限制
3. **动画性能问题**: 在低性能设备上禁用复杂动画

## 测试策略

### 样式测试

1. **视觉回归测试**: 使用截图对比检测样式变化
2. **响应式测试**: 在不同设备尺寸下测试布局
3. **浏览器兼容性测试**: 在主流浏览器中测试样式表现

### 组件测试

1. **单元测试**: 测试组件的基本功能和属性
2. **集成测试**: 测试组件间的交互和布局
3. **端到端测试**: 测试完整的用户交互流程

## 性能优化

### CSS 优化

1. **代码分割**: 按页面和组件分割 CSS
2. **压缩优化**: 使用 CSS 压缩工具减小文件大小
3. **缓存策略**: 合理设置 CSS 文件的缓存策略

### 运行时优化

1. **动画优化**: 使用 GPU 加速的 CSS 属性
2. **重排重绘优化**: 避免频繁的样式计算
3. **懒加载**: 对非关键样式进行懒加载

## 可访问性设计

### 键盘导航

1. **焦点管理**: 确保所有交互元素可通过键盘访问
2. **焦点指示器**: 提供清晰的焦点视觉反馈
3. **跳转链接**: 提供快速跳转到主要内容的链接

### 屏幕阅读器支持

1. **语义化标签**: 使用正确的 HTML 语义标签
2. **ARIA 标签**: 为复杂组件提供 ARIA 标签
3. **文本替代**: 为图片和图标提供文本描述

### 颜色和对比度

1. **对比度检查**: 确保文字和背景有足够的对比度
2. **色盲友好**: 不仅依赖颜色传达信息
3. **高对比度模式**: 支持系统的高对比度设置

## 国际化支持

### 文字方向

1. **RTL 支持**: 支持从右到左的文字方向
2. **文字长度适配**: 适应不同语言的文字长度变化

### 字体系统

1. **多语言字体**: 支持不同语言的字体显示
2. **字体回退**: 提供合适的字体回退方案

## 维护和扩展

### 代码组织

1. **模块化**: 样式代码按功能模块组织
2. **命名规范**: 使用 BEM 或类似的命名规范
3. **文档化**: 为样式系统提供完整的文档

### 版本管理

1. **变更日志**: 记录样式系统的重要变更
2. **向后兼容**: 保持 API 的向后兼容性
3. **迁移指南**: 提供版本升级的迁移指南