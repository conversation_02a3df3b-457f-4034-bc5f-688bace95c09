<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关注列表</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui@2.15.14/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2.7.16/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui@2.15.14/lib/index.js"></script>
    <script src="https://unpkg.com/axios@1.6.8/dist/axios.min.js"></script>
    <script src="https://unpkg.com/dompurify@3.2.3/dist/purify.min.js"></script>
    <script src="https://unpkg.com/luxon@3.5.0/build/global/luxon.min.js"></script>

    <style>
        body {
            font-family: sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
        }

        #app {
            max-width: 960px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .user-list {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: flex-start; /* 左对齐 */
        }

        .user-card {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: transform 0.2s ease-in-out;
            width: calc(33% - 20px); /*  三列布局，考虑间距 */
            min-width: 200px;
            box-sizing: border-box;

        }

        .user-card:hover {
            transform: translateY(-3px);
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-right: 15px;
            object-fit: cover;
            cursor: pointer;
        }

        .user-info {
            flex-grow: 1;
        }

        .user-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .user-stats {
            color: #777;
            font-size: 12px;
        }
        .user-stats span {
            margin-right: 10px;
        }

        /* 头像放大预览样式 */
        .avatar-dialog .el-dialog__body {
            padding: 0;
        }

        .avatar-dialog img {
            width: 100%;
            display: block;
        }
        @media (max-width: 768px) {
            .user-card {
                width: calc(50% - 20px); /* 两列布局 */
            }
        }

        @media (max-width: 576px) {
            .user-card {
                width: 100%; /* 单列布局 */
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <h1>我的关注列表</h1>

        <el-button type="danger" @click="clearCookies" v-if="isDebug">清除 Cookies</el-button>

        <el-alert
            v-if="loading"
            title="加载中..."
            type="info"
            show-icon>
        </el-alert>

        <el-alert
            v-if="error"
            :title="error"
            type="error"
            show-icon>
        </el-alert>

        <div class="user-list" v-if="users.length > 0">
            <div class="user-card" v-for="user in users" :key="user.id" @click="handleUserClick(user)">
                <img :src="getProxyImageUrl(user.profile_image_url)" alt="头像" class="user-avatar" @click.stop="showAvatar(user.avatar_hd)">
                <div class="user-info">
                    <div class="user-name">{{ user.screen_name }}</div>
                    <div class="user-stats">
                        <span>评论: {{ user.status_total_counter.comment_cnt }}</span>
                        <span>点赞: {{ user.status_total_counter.like_cnt }}</span>
                        <span>转发: {{ user.status_total_counter.repost_cnt }}</span>
                    </div>
                </div>
            </div>
        </div>

        <el-dialog :visible.sync="dialogVisible" class="avatar-dialog">
            <img :src="dialogImageUrl" v-if="dialogImageUrl" alt="大图">
        </el-dialog>

        <div v-else-if="!loading && !error">
            <el-empty description="暂无关注"></el-empty>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data: {
                users: [],
                loading: false,
                error: null,
                dialogImageUrl: '',
                dialogVisible: false,
                isDebug: false
            },
            mounted() {
                this.fetchData();
                this.checkDebugMode(); // 检查 debug 模式
            },
            methods: {
                checkDebugMode() {
                    const urlParams = new URLSearchParams(window.location.search);
                    this.isDebug = urlParams.has('debug');
                },
                async fetchData() {
                    this.loading = true;
                    this.error = null;
                    try {
                        const response = await axios.get('/api/get_subscribe');
                        if (response.data) {
                            if (response.data.errcode === 0) {
                                this.users = response.data.data;
                            } else if (response.data.errcode === 1) {
                                // 重定向到 OAuth2 登录页面
                                let redirectUrl = `/api/oauth2?redirect=${encodeURIComponent(window.location.href)}`;
                                if (this.isDebug) {
                                    redirectUrl += '&debug'; // 添加 debug 参数
                                }
                                window.location.href = redirectUrl;
                                return; // 阻止继续执行
                            } else {
                                this.error = response.data.message || '数据加载失败，请稍后重试。';
                            }
                        } else {
                            this.error = '数据加载失败，请稍后重试。';
                        }
                    } catch (error) {
                        console.error('Error fetching data:', error);
                        this.error = '网络错误，请检查您的网络连接。';
                    } finally {
                        this.loading = false;
                    }
                },
                handleUserClick(user) {
                    this.$message({
                        message: `目标用户: ${user.screen_name}`,
                        type: 'success'
                    });
                    Vue.nextTick(() => {
                        setTimeout(() => {
                            window.location.href = `/user/${user.id}`;
                        }, 500);
                    });
                },
                showAvatar(url) {
                    this.dialogImageUrl = this.getProxyImageUrl(url);
                    this.dialogVisible = true;
                },
                getProxyImageUrl(url) {
                    if (!url) return '';
                    return `https://image.baidu.com/search/down?url=${encodeURIComponent(url)}`;
                },
                clearCookies() {
                    const cookies = document.cookie.split(";");

                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i];
                        const eqPos = cookie.indexOf("=");
                        const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
                        document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT";
                        document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;domain=" + document.domain + ";path=/"; // 尝试设置 domain 和 path
                    }

                    // 发送 /api/logout 请求
                    axios.get('/api/logout')
                        .then(response => {
                            if (response.data && response.data.errcode === 0) {
                                this.$message({
                                    message: 'Cookies 已清除，已成功注销!',
                                    type: 'success'
                                });
                            } else {
                                this.$message({
                                    message: 'Cookies 已清除，但注销失败!',
                                    type: 'warning'
                                });
                            }
                        })
                        .catch(error => {
                            console.error('Logout failed:', error);
                            this.$message({
                                message: 'Cookies 已清除，但注销请求失败!',
                                type: 'error'
                            });
                        });
                }
            }
        })
    </script>
</body>
</html>