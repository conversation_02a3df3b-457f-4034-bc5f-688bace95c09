import { imageProxy } from './imageProxy'

/**
 * 从微博数据中提取图片URL列表
 * @param {Object} card - 微博卡片数据
 * @returns {Array} 处理后的图片URL数组
 */
export const extractImageUrls = (card) => {
  if (!card) return []
  
  let images = []
  
  if (card.pic_infos) {
    images = getImageUrlsFromPicInfos(card.pic_infos)
  } else if (card.pics) {
    images = getImageUrlsFromPics(card.pics)
  }
  
  return images
    .map(url => imageProxy.proxyImage(url))
    .filter(url => url && url.trim() !== '')
}

/**
 * 从 pic_infos 对象中提取图片URL
 * @param {Object} picInfos - 图片信息对象
 * @returns {Array} 图片URL数组
 */
const getImageUrlsFromPicInfos = (picInfos) => {
  if (!picInfos) return []
  
  return Object.values(picInfos).map(pic => {
    return pic.large?.url || pic.original?.url || ''
  }).filter(Boolean)
}

/**
 * 从 pics 数组中提取图片URL
 * @param {Array} pics - 图片数组
 * @returns {Array} 图片URL数组
 */
const getImageUrlsFromPics = (pics) => {
  if (!pics) return []
  
  return pics.map(pic => {
    return pic.large?.url || pic.original?.url || pic.url || ''
  }).filter(Boolean)
}