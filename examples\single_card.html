<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="https://m.weibo.cn/favicon.ico" type="image/x-icon">
    <!-- import CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui@2.15.14/lib/theme-chalk/index.css">
    <style>
        .text {
            font-size: 14px;
        }

        .item {
            margin-bottom: 18px;
        }

        .clearfix:before,
        .clearfix:after {
            display: table;
            content: "";
        }

        .clearfix:after {
            clear: both;
        }

        .box-card {
            width: 90%;
            margin: 10px;
        }

        .card-title {
            display: flex;
            align-items: center;
            color: deeppink;
        }

        /* 微博内嵌表情 */
        img[alt] {
            width: 16px;
        }

        /* 设置链接的颜色为红色 */
        a {
            color: rgb(66, 139, 248);
            text-decoration: none; /* 取消下划线 */
        }

        /* 设置访问过的链接的颜色为绿色 */
        a:visited {
            color: rgb(66, 139, 248);
        }
    </style>
</head>

<body>
    <div id="app">

        <el-card class="box-card" v-if="card" style="padding: 10px;">
            <div slot="header" class="clearfix">
                <span class="card-title" style="width: 100%; display: flex; ">
                    <el-avatar size="medium" :src="card.user.wx_avatar_hd ? card.user.wx_avatar_hd : 'https://image.baidu.com/search/down?url=' + card.user.avatar_hd"></el-avatar>
                    <span style="padding-left: 10px; font-size: 14px;">{{ card.user.screen_name }}</span>
                    <div style="font-size: 10px; margin-left: auto;">
                        <span style="color: gray; display: block;">{{ getDateStrCn(card.created_at) }}</span>
                        <span style="color: skyblue; display: block;">{{ card.region_name }}</span>
                        <span style="color: orange; display: block;">来自 <span v-html="card.source"></span></span>
                    </div>
                </span>
            </div>

            <!-- <div>{{ card.text }}</div> -->
            <div v-html="card.text"></div>

            <div class="demo-image__preview">
                <el-image v-for="(url, index) in retweetPics(card.pic_infos)" style="width: 100px; height: 100px; padding: 5px;" :src="url" :key="index"
                    :preview-src-list="retweetPics(card.pic_infos)" fit="cover">
                </el-image>
            </div>

            <el-divider></el-divider>

            <!-- 转发微博逻辑 -->
            <div v-if="card.retweeted_status">
                <el-card v-if="card" style="padding: 10px;width: 100%;">  
                    <div slot="header" class="clearfix" style="color: deeppink;">
                        <span>@</span>
                        <span>{{ card.retweeted_status.user.screen_name }}</span>
                        <span>:</span>
                    </div>

                    <!-- <span>{{ card.retweeted_status.user.text }}</span> -->
                    <span v-html="card.retweeted_status.text"></span>

                    <div class="demo-image__preview" style="padding-top: 16px;">
                        <el-image v-for="(url, index) in retweetPics(card.retweeted_status.pic_infos)" style="width: 100px; height: 100px; padding: 5px;" :src="url" :key="index"
                            :preview-src-list="retweetPics(card.retweeted_status.pic_infos)" fit="cover">
                        </el-image>
                    </div>

                </el-card>
            <el-divider></el-divider>

            </div>


            <el-link type="danger">警告: 如果你的浏览器登陆了微博, 可能会在对方主页留下访客记录!</el-link>
            <br />
            <el-link type="danger" :href="'https://m.weibo.cn/detail/'+card.id" target="_blank" style="padding: 10px;">
                <el-button type="danger">跳转到原始微博</el-button>
            </el-link>
            <el-link type="success" :href="'https://weibo.api.maikebuke.com/user/'+card.user.id" target="_blank" style="padding: 10px;">
                <el-button type="success">查看用户历史微博</el-button>
            </el-link>
        </el-card>
        <div v-else>
            Loading ...
        </div>
    </div>
</body>
<!-- import Vue before Element -->
<script src="https://unpkg.com/vue@2.7.16/dist/vue.js"></script>
<!-- import JavaScript -->
<script src="https://unpkg.com/element-ui@2.15.14/lib/index.js"></script>
<!-- import axios -->
<script src="https://unpkg.com/axios@1.6.8/dist/axios.min.js"></script>
<script>
    new Vue({
        el: '#app',
        data: function () {
            return {
                card: null,
            };
        },
        mounted() {
            // 获取 URL 中的参数
            const urlParams = new URLSearchParams(window.location.search);
            // 获取名为 id 的参数值
            const weibo_id = urlParams.get('id');
            // 获取远程的 card json
            axios.get('/data/weibo/ajax/' + weibo_id + '.json')
            .then(response => {
                this.card = response.data;
            })
            .catch(error => {
                console.error('Error fetching data:', error);
            });
        },
        methods: {
            jumpToSource() {
                const weibo_id = this.card.id;
                window.location.href = "https://m.weibo.cn/detail/" + weibo_id;
            },
            retweetPics(pics_array) {
                let relink_baidu;
                const pics = [];
                // const retweet_pics = this.card.retweeted_status.pic_infos;
                const retweet_pics = pics_array;
                for (let key in retweet_pics) {
                    const image_type = "largest";
                    if (retweet_pics.hasOwnProperty(key) && retweet_pics[key].hasOwnProperty(image_type)) {
                        const url = retweet_pics[key][image_type]['url'];
                        // WordPress 图片缓存服务(不能有前缀的https://): https://i0.wp.com/wx1.sinaimg.cn/large/001O3KAmgy1how1ilh7fnj63lp5eg7wl02.jpg
                        // https://www.codenong.com/getimg_baidu.php?url=
                        relink_baidu = "https://image.baidu.com/search/down?url=" + url;
                        pics.push(relink_baidu);
                    }
                }
                return pics;
            },
            getDateStrCn(dateStrUTC) {
                const dateObj = new Date(dateStrUTC);
                // 获取年、月、日、时、分、秒
                let year = dateObj.getFullYear();
                let month = dateObj.getMonth() + 1;  // JavaScript的月份是从0开始的，所以需要加1
                let day = dateObj.getDate();
                let hour = dateObj.getHours();
                let minute = dateObj.getMinutes();
                let second = dateObj.getSeconds();

                // 将单位数字转换为两位数字
                month = month < 10 ? '0' + month : month;
                day = day < 10 ? '0' + day : day;
                hour = hour < 10 ? '0' + hour : hour;
                minute = minute < 10 ? '0' + minute : minute;
                second = second < 10 ? '0' + second : second;

                // 组装成字符串
                const cnDateString = `${year}年${month}月${day}日 ${hour}:${minute}:${second}`;
                return cnDateString;
            },
        },
        computed: {
            // 计算属性
        },
    })
</script>

</html>

<!-- 
{{ card_info }}
 -->
 