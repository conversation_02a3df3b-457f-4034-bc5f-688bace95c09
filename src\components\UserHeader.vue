<template>
  <div class="user-header">
    <div class="user-info">
      <div class="avatar-container">
        <el-avatar :src="avatarUrl" class="user-avatar"></el-avatar>
      </div>
      <div class="user-details">
        <div class="user-name">{{ user.screen_name }}</div>
        <div class="user-meta" v-if="user.followers_count || user.verified">
          <span v-if="user.verified" class="verified-badge">
            <el-icon><Check /></el-icon>
          </span>
          <span v-if="user.followers_count" class="followers-count">
            {{ formatFollowersCount(user.followers_count) }} 粉丝
          </span>
        </div>
      </div>
    </div>
    <div class="post-info">
      <div class="post-date">{{ formattedDate }}</div>
      <div class="post-source" v-if="source" v-html="formatText(source)"></div>
      <div class="post-location" v-if="regionName">{{ regionName }}</div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { Check } from '@element-plus/icons-vue'
import { imageProxy } from '../utils/imageProxy'
import { textFormatter } from '../utils/textFormatter'
import { DateTime } from 'luxon'

export default {
  name: 'UserHeader',
  components: {
    Check
  },
  props: {
    user: {
      type: Object,
      required: true
    },
    createdAt: {
      type: String,
      required: true
    },
    source: {
      type: String,
      default: ''
    },
    regionName: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const avatarUrl = computed(() => {
      return imageProxy.proxyImage(props.user.profile_image_url)
    })

    const formattedDate = computed(() => {
      const cardTime = new Date(props.createdAt)
      const dateTime = DateTime.fromMillis(cardTime.getTime())
      return dateTime.setLocale('zh').toFormat('yyyy年MM月dd日 HH:mm:ss')
    })

    const formatText = (text) => {
      return textFormatter.formatText(text)
    }

    const formatFollowersCount = (count) => {
      if (count >= 10000) {
        return `${(count / 10000).toFixed(1)}万`
      }
      return count.toString()
    }

    return {
      avatarUrl,
      formattedDate,
      formatText,
      formatFollowersCount
    }
  }
}
</script>

<style lang="scss" scoped>
// 主容器样式
.user-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: $space-lg;
  padding: 0;

  @include mobile {
    gap: $space-md;
    align-items: center;
  }
}

// 用户信息区域
.user-info {
  display: flex;
  align-items: center;
  gap: $space-md;
  flex: 1;
  min-width: 0;

  @include mobile {
    gap: $space-sm;
  }
}

// 头像容器
.avatar-container {
  flex-shrink: 0;
  position: relative;
}

// 头像样式优化
.user-avatar {
  width: 48px !important;
  height: 48px !important;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid rgba(255, 255, 255, 0.8);
  box-shadow: $shadow-1;
  transition: all $transition-base;
  background: $bg-secondary;

  &:hover {
    border-color: $primary-color;
    transform: scale(1.08);
    box-shadow: $shadow-2;
  }

  @include mobile {
    width: 42px !important;
    height: 42px !important;
    border-width: 2px;
  }
}

// 用户详情区域
.user-details {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: $space-xs;
}

// 用户名样式
.user-name {
  font-weight: 700;
  color: $text-primary;
  font-size: $font-size-lg;
  line-height: $line-height-tight;
  margin: 0;
  @include text-ellipsis;
  letter-spacing: -0.02em;

  @include mobile {
    font-size: $font-size-base;
    font-weight: 600;
  }
}

// 用户元信息
.user-meta {
  display: flex;
  align-items: center;
  gap: $space-sm;
  font-size: $font-size-xs;
  color: $text-tertiary;

  @include mobile {
    gap: $space-xs;
    font-size: 11px;
  }
}

// 认证徽章
.verified-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #1677ff, #4096ff);
  border-radius: 50%;
  color: white;
  font-size: 10px;
  flex-shrink: 0;

  @include mobile {
    width: 14px;
    height: 14px;
    font-size: 9px;
  }
}

// 粉丝数量
.followers-count {
  font-weight: 500;
  color: $text-secondary;
  white-space: nowrap;
}

// 发布信息样式
.post-info {
  display: flex;
  flex-direction: column;
  gap: $space-xs;
  align-items: flex-end;
  flex-shrink: 0;
  text-align: right;
  min-width: 120px;

  @include mobile {
    gap: 2px;
    min-width: 100px;
    align-items: flex-start;
    text-align: left;
  }
}

// 发布日期
.post-date {
  font-weight: 600;
  color: $text-secondary;
  font-size: $font-size-xs;
  white-space: nowrap;
  padding: $space-xs $space-sm;
  background: linear-gradient(135deg, rgba(22, 119, 255, 0.08), rgba(64, 150, 255, 0.05));
  border-radius: $radius-lg;
  border: 1px solid rgba(22, 119, 255, 0.1);
  transition: all $transition-fast;

  &:hover {
    background: linear-gradient(135deg, rgba(22, 119, 255, 0.12), rgba(64, 150, 255, 0.08));
    border-color: rgba(22, 119, 255, 0.2);
    transform: translateY(-1px);
  }

  @include mobile {
    font-size: 11px;
    padding: 2px $space-xs;
    font-weight: 500;
  }
}

// 发布来源
.post-source {
  font-size: $font-size-xs;
  color: $text-tertiary;
  white-space: nowrap;
  transition: color $transition-fast;

  &:hover {
    color: $text-secondary;
  }

  @include mobile {
    font-size: 10px;
  }
}

// 发布位置
.post-location {
  font-size: $font-size-xs;
  color: $text-tertiary;
  white-space: nowrap;
  padding: 2px $space-xs;
  background: rgba(134, 144, 156, 0.1);
  border-radius: $radius-sm;
  transition: all $transition-fast;

  &:hover {
    background: rgba(134, 144, 156, 0.15);
    color: $text-secondary;
  }

  @include mobile {
    font-size: 10px;
    padding: 1px $space-xs;
  }
}

// 来源信息中的链接样式
:deep(.post-source) {
  a {
    color: inherit;
    text-decoration: none;
    transition: color $transition-fast;

    &:hover {
      color: $primary-color;
    }
  }

  // 标签样式
  .topic-tag,
  .at-user-tag {
    display: inline-block;
    padding: 1px 4px;
    border-radius: $radius-sm;
    font-size: 9px;
    font-weight: 500;
    margin: 0 1px;
    transition: all $transition-fast;

    @include mobile {
      font-size: 8px;
      padding: 1px 3px;
    }
  }

  .topic-tag {
    color: $primary-color;
    background: $primary-light;

    &:hover {
      background: $primary-color;
      color: white;
      transform: translateY(-1px);
    }
  }

  .at-user-tag {
    color: $primary-color;
    background: $primary-light;

    &:hover {
      background: $primary-color;
      color: white;
      transform: translateY(-1px);
    }
  }
}

// 暗色模式适配
@include dark-mode {
  .user-name {
    color: $dark-text-primary;
  }

  .user-meta {
    color: $dark-text-tertiary;
  }

  .followers-count {
    color: $dark-text-secondary;
  }

  .user-avatar {
    border-color: rgba(255, 255, 255, 0.1);
    background: $dark-bg-secondary;

    &:hover {
      border-color: $primary-color;
    }
  }

  .post-date {
    color: $dark-text-secondary;
    background: linear-gradient(135deg, rgba(22, 119, 255, 0.15), rgba(64, 150, 255, 0.08));
    border-color: rgba(22, 119, 255, 0.2);

    &:hover {
      background: linear-gradient(135deg, rgba(22, 119, 255, 0.2), rgba(64, 150, 255, 0.12));
      border-color: rgba(22, 119, 255, 0.3);
    }
  }

  .post-source {
    color: $dark-text-tertiary;

    &:hover {
      color: $dark-text-secondary;
    }
  }

  .post-location {
    color: $dark-text-tertiary;
    background: rgba(255, 255, 255, 0.05);

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: $dark-text-secondary;
    }
  }
}

// 动画效果
@keyframes avatarPulse {
  0%, 100% {
    box-shadow: $shadow-1;
  }
  50% {
    box-shadow: $shadow-2;
  }
}

// 头像加载动画
.user-avatar {
  &.loading {
    animation: avatarPulse 2s ease-in-out infinite;
  }
}

// 微交互优化
.user-info:hover {
  .user-avatar {
    transform: scale(1.05);
  }

  .user-name {
    color: $primary-color;
  }
}

// 无障碍优化
.user-header {
  &:focus-within {
    .user-avatar {
      outline: 2px solid $primary-color;
      outline-offset: 2px;
    }
  }
}

// 打印样式优化
@media print {
  .user-header {
    break-inside: avoid;

    .post-info {
      display: none;
    }

    .user-avatar {
      border: 1px solid #000;
      box-shadow: none;
    }
  }
}
</style>